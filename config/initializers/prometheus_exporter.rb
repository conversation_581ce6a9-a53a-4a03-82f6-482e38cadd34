# frozen_string_literal: true

if PROMETHEUS_AVAILABLE
  require 'prometheus_exporter/middleware'
  require 'prometheus_exporter/client'
  require 'prometheus_exporter/instrumentation'

  client = PrometheusExporter::Client.new(host: ENV['PROMETHEUS_SERVICE_URL'], port: ENV['PROMETHEUS_SERVICE_PORT'])
  PrometheusExporter::Client.default = client

  Rails.application.middleware.unshift PrometheusExporter::Middleware

  PrometheusExporter::Instrumentation::Process.start(type: 'master')
end

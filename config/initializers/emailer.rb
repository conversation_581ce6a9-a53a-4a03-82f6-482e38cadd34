# frozen_string_literal: true

EMAILER_AUTH_TOKEN = ENV['EMAILER_AUTH_TOKEN']
EMAILER_DELAY_SECONDS = ENV.fetch('EMAILER_DELAY_SECONDS') { 0 }.to_i
EMAILER_HOST = ENV['EMAILER_HOST'] # { 'http://email-service-staging.oc.video.rt.ru/api/v1/send' }

PASSWORD_RECOVERY_TEMPLATE = 'auth-password-recovery'

FUNCTIONAL_CHIEF_APPROVE_UNSCHEDULED = 'functional-chief-approve-unscheduled'
LINE_CHIEF_APPROVE_UNSCHEDULED = 'linear-chief-approve-unscheduled'
LINE_CHIEF_APPROVE_UNSCHEDULED_URGENTLY = 'linear-chief-approve-unscheduled-urgently'
LINE_CHIEF_APPROVE_UNSCHEDULED_LAST_DAY = 'linear-chief-approve-unscheduled-last-day'
ESCALATED_CHIEF_APPROVE_UNSCHEDULED = 'escalated-chief-approve-unscheduled'
EMPLOYEE_APPROVE_UNSCHEDULED = 'unscheduled-approved'
EMPLOYEE_PARTIAL_APPROVE_UNSCHEDULED = 'unscheduled-partially-approved'
EMPLOYEE_REJECTED_UNSCHEDULED_WITH_ADVANCE = 'unscheduled-rejected-with-advance'
EMPLOYEE_REJECTED_UNSCHEDULED = 'unscheduled-rejected'
EMPLOYEE_REJECTED_UNSCHEDULED_CHIEF_NOT_HAVE_TIME_WITH_ADVANCE = 'linear-chief-did-not-have-time-to-approve-unscheduled-with-advance'
EMPLOYEE_REJECTED_UNSCHEDULED_CHIEF_NOT_HAVE_TIME_WITHOUT_ADVANCE= 'linear-chief-did-not-have-time-to-approve-unscheduled'
EMPLOYEE_UNSCHEDULED_STILL_NOT_APPROVED = 'unscheduled-still-not-approved'

REPLACEMENT_TEMPORARY_CANCEL = 'replacement-temporary-cancel'
REPLACEMENT_CANCEL = 'replacement-cancel'
REPLACEMENT_FINISHED = 'replacement-finished'
REPLACEMENT_APPOINTMENT = 'replacement-appointment'

# "Обязательный отпуск [письма]"
NOTIFY_CHIEF_MANDATORY_WILL_BE_APPROVE = 'to-chief-required-rejected'
NOTIFY_EMPLOYEE_MANDATORY_WILL_BE_APPROVE = 'to-employee-required-rejected'
TO_EMPLOYEE_PARTIALLY_APPROVED = 'to-employee-partially-approved'
CHIEF_AUTOMATED_VACATION_MAKE = 'chief-automated-vacation-make'
EMPLOYEE_AUTOMATED_VACATION_MAKE = 'employee-automated-vacation-make'
EMPLOYEE_AUTOMATED_VACATION_URGENT_REQUIRED_MAKE = 'employee-automated-vacation-urgent-required-make'
EMPLOYEE_AUTOMATED_VACATION_URGENT_MAKE = 'employee-automated-vacation-urgent-make'
NOTIFY_EMPLOYEE_VACATION_COMING = 'employee-upcoming-vacation'
NOTIFY_EMPLOYEE_CONFIRMATION_TIME_EXPIRED = 'employee-confirmation-period-expired'

LINE_CHIEF_APPROVE_RESCHEDULE = 'linear-chief-approve-transfer'
FUNCTIONAL_CHIEF_APPROVE_RESCHEDULE = 'functional-chief-approve-transfer'
ESCALATED_CHIEF_APPROVE_RESCHEDULE = 'escalated-chief-approve-transfer'
LINE_CHIEF_APPROVE_RESCHEDULE_URGENTLY = 'linear-chief-approve-transfer-urgently'
FUNCTIONAL_CHIEF_APPROVE_RESCHEDULE_URGENTLY = 'functional-chief-approve-transfer-urgently'
LINE_CHIEF_APPROVE_RESCHEDULE_LAST_DAY = 'linear-chief-approve-transfer-last-day'
FUNCTIONAL_CHIEF_APPROVE_RESCHEDULE_LAST_DAY = 'functional-chief-approve-transfer-last-day'
ESCALATED_CHIEF_APPROVE_RESCHEDULE_LAST_DAY = 'escalated-chief-approve-transfer-last-day'
EMPLOYEE_RESCHEDULE_APPROVED = 'employee-transfer-approved'
EMPLOYEE_RESCHEDULE_APPROVED_PARTIALLY = 'employee-transfer-approved-partially'
EMPLOYEE_RESCHEDULE_NOT_APPROVED = 'employee-transfer-not-approved'
EMPLOYEE_RESCHEDULE_STILL_NOT_APPROVED = 'employee-transfer-still-not-approved'
EMPLOYEE_RESCHEDULE_NOT_APPROVED_IN_TIME = 'employee-transfer-not-approved-chief-had-no-time'
EMPLOYEE_CHIEF_APPROVE_SCHEDULED = 'linear-chief-approve-scheduled'

CHIEF_APPROVE_CANCELLATION = 'chief-cancellation-should-approve'
EMPLOYEE_CANCELLATION_APPROVED = 'employee-cancellation-approve'
EMPLOYEE_CANCELLATION_PARTIAL_APPROVE = 'employee-cancellation-partially-approve'

EMPLOYEE_CANCELLATION_NOT_HAVE_TIME_TO_AGREED = 'employee-cancellation-chief-have-no-time-to-approve'
EMPLOYEE_CANCELLATION_REJECTED = 'employee-cancellation-rejected'

CHIEF_APPROVE_RECALL = 'chief-recall-should-approve'
EMPLOYEE_RECALL_APPROVE = 'employee-recall-approve'
EMPLOYEE_RECALL_NOT_HAVE_TIME_TO_AGREED = 'employee-recall-chief-have-no-time-to-approve'

EMPLOYEE_RECALL_PARTIAL_APPROVE = 'employee-recall-partially-approve'
FUNCTIONAL_CHIEF_APPROVE_CANCELLATION_AND_TRANSFER = 'functional-chief-approve-cancellation-and-transfer'
LINE_CHIEF_APPROVE_CANCELLATION_AND_TRANSFER = 'linear-chief-approve-cancellation-and-transfer'
# TODO: название шаблона уточнить у Желудков С.
# EMPLOYEE_CANCELLATION_NOT_HAVE_TIME_TO_AGREED = 'chief-cancellation-not-approve'
EMPLOYEE_RECALL_REJECTED = 'employee-recall-rejected'
# EMPLOYEE_CANCELLATION_REJECTED = 'employee-cancellation-not-approve'
# EMPLOYEE_RECALL_APPROVE = 'employee-recall-approve'


# Кафетерий
CAFETERIA_ADMIN_NPF = 'benefits-deduction-employee-npf'
CAFETERIA_INCORRECT_PAYMENT_TEMPLATE = 'benefits-error-receiving-amount-from-1C'
CANCEL_BENEFIT_SERVICE_NOTE_NOTIFICATION = 'benefits-reject-of-application'

EMPLOYEE_HAS_ADDITIONAL_VACATIONS_PAID = 'to-combinator-second-rate-vacation-with-payment'
EMPLOYEE_HAS_ADDITIONAL_VACATIONS_UNPAID = 'to-combinator-second-rate-vacation-without-payment'

# Согласование отпуска письма для руководителя
NOTIFY_CHIEF_APPROVE_VACATION = 'functional-chief-need-approve-vacation'
NOTIFY_LINE_CHIEF_APPROVE_VACATION = 'linear-chief-need-approve-vacation'
NOTIFY_CHIEF_APPROVE_NOW = 'functional-chief-need-urgently-approve-vacation'
NOTIFY_LINE_CHIEF_APPROVE_NOW = 'linear-chief-need-urgently-approve-vacation'
LINE_CHIEF_LAST_DAY_VACATION_NOTIFICATION = 'linear-chief-approve-vacation-last-day'
FUNCTIONAL_CHIEF_LAST_DAY_VACATION_NOTIFICATION = 'functional-chief-approve-vacation-last-day'
NOTIFY_ESCALATED_CHIEF_APPROVE_VACATION = 'escalated-chief-need-approve-vacation'
NOTIFY_CHIEF_ABOUT_AUTO_AGREEMENT = 'chief-auto-approve-vacation'

# Согласование отпуска письма для сотрудника
CHIEF_AGREED_VACATION =  'to-employee-partially-approved'
NOTIFY_EMPLOYEE_VACATION_REJECTED = 'to-employee-rejected'
NOTIFY_EMPLOYEE_VACATION_NOT_AGREED = 'to-employee-still-not-approve'
NOTIFY_EMPLOYEE_VACATION_APPROVED = 'to-employee-approved'

NOTIFY_EMPLOYEE_VACATION_DATES_CHANGED = 'to-employee-vacation-dates-changed'

# "Карьерное консультирование"
EMPLOYEE_NEW_REQUEST_FOR_CREATE_CONSULTATION = 'new-request-for-create-consultation'
CAREER_CONSELING_MEETING = 'career-counseling-meeting'
CAREER_CONSULTING_MEETING_CHANGE = 'consultant-reschedules-consultation'
CAREER_CONSULTING_MEETING_CANCEL = 'consultant-cancel-consultation'
CAREER_CONSULTING_MEETING_FEEDBACK = 'employee-feedback-the-consultation'
CAREER_CONSULTING_ASLING_RESULT = 'consultant-ask-consultation-result'

CHIEF_REMOVED_FROM_VACATION = 'functional-chief-remove-from-vacation'

# Благодарности
NEW_GRATITUDE_EARN = 'new-gratitude-earn'

# Открытки
HOLIDAY_CARD_TEMPLATE_ID = 'holiday-postcard'

# ----
RESCHEDULE_REJECTION_AFTER_FIRE_DATE = 'Перенос отпусков был автоматически отклонён в связи с увольнением'
RESCHEDULE_ORDINARY_AFTER_FIRE_DATE = 'Отпуск был автоматически отклонён в связи с увольнением'
RESCHEDULE_WITHOUT_SCHEDULE_AFTER_FIRE_DATE = 'Отпуск(а) был автоматически отклонён в связи с увольнением'

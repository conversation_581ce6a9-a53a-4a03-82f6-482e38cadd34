# frozen_string_literal: true

Rails.application.configure do
  config.hosts = nil

  # Settings specified here will take precedence over those in config/application.rb.
  # In the development environment your application's code is reloaded on
  # every request. This slows down response time but is perfect for development
  # since you don't have to restart the web server when you make code changes.
  config.cache_classes = false

  # Do not eager load code on boot.
  config.eager_load = true

  # Show full error reports.
  config.consider_all_requests_local = true
  config.action_controller.perform_caching = true
  config.session_store :cache_store, key: "_#{Rails.application.class.module_parent.name.downcase}_session", expire_after: 30.days

  # Enable/disable caching. By default caching is disabled.
  # Run rails dev:cache to toggle caching.

  config.web_console.whiny_requests = false

  # Store uploaded files on the local file system (see config/storage.yml for options).
  config.active_storage.service = ENV.fetch("ACTIVE_STORAGE_SERVICE") { 'minio' }

  # Don't care if the mailer can't send.
  config.action_mailer.raise_delivery_errors = false
  config.action_mailer.perform_deliveries = false

  # config.action_mailer.perform_caching = false

  config.action_mailer.smtp_settings = {
    address: ENV.fetch('SMTP_HOST'),
    port: ENV.fetch('SMTP_PORT'),
    authentication: ENV.fetch('SMTP_AUTH') { 'plain' }.to_sym,
    user_name: ENV.fetch('SMTP_USER') { '' },
    password: ENV.fetch('SMTP_PASSWORD') { '' },
    enable_starttls_auto: ENV['IS_SMTP_TLS'].present?
  }

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  logger           = ActiveSupport::Logger.new(STDOUT)
  logger.formatter = config.log_formatter
  config.logger    = ActiveSupport::TaggedLogging.new(logger)


  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  config.telegram_updates_controller.session_store = :redis_cache_store, { url: ENV['REDIS_HOST'], expires_in: 1.month, namespace: 'cache' }

  # Highlight code that triggered database queries in logs.
  config.active_record.verbose_query_logs = true

  # Debug mode disables concatenation and preprocessing of assets.
  # This option may cause significant delays in view rendering with a large
  # number of complex assets.
  config.assets.debug = true

  # Suppress logger output for asset requests.
  config.assets.quiet = true

  # Raises error for missing translations.
  # config.action_view.raise_on_missing_translations = true

  # Use an evented file watcher to asynchronously detect changes in source code,
  # routes, locales, etc. This feature depends on the listen gem.
  config.file_watcher = ActiveSupport::EventedFileUpdateChecker

  config.log_level = ENV.fetch('LOG_LEVEL') { 'debug' }.to_sym
end

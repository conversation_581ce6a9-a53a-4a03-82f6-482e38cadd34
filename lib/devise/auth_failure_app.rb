# frozen_string_literal: true

class AuthFailureApp < Devise::FailureApp
  # warden, а соответственно и devise, устроен так,
  # что обработка ошибок происходит в отдельных сущностях - FailureApp.
  # Наследуемся от исходного FailureApp с целью возвращать json в случае ошибки,
  # а не рендерить html
  # Выкинуть тут ексепшн и обработать его в контроллере нельзя,
  # так как это уже обработка експешна, который кидает warden
  # и обработка эта происходит после экшна в контроллере,
  # который иницирует аутенфикацию.
  #
  # Также вызывается при попытке совершить неаутентифицируемое действие
  #
  # Устанавливается в inititalizer'е devise.
  # Ссылка на исходный FailureApp:
  # https://github.com/plataformatec/devise/blob/master/lib/devise/failure_app.rb
  def respond
    _set_response
  end

  private

  def _set_response
    status_code = 401
    err = i18n_message

    self.status = status_code
    self.content_type = 'application/json'
    self.response_body = JSON.generate(error: err)
  end
end

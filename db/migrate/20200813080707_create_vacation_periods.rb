class CreateVacationPeriods < ActiveRecord::Migration[6.0]
  def change
    create_table :vacation_periods do |t|
      t.date :start_date
      t.date :end_date
      t.integer :days
      t.string :chief_number
      t.string :vacation_type
      t.integer :payment_status
      t.string :order_number
      t.integer :vacation_id

      t.index [:start_date, :vacation_id], unique: true

      t.timestamps
    end
    add_foreign_key :vacation_periods, :vacations, on_delete: :cascade
  end
end

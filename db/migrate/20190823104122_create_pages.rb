class CreatePages < ActiveRecord::Migration[6.0]
  def change
    create_table :pages do |t|
      t.string :title, null: false
      t.text :description
      t.text :body

      t.integer :status, default: 0
      
      t.string :setka_theme_id
      t.string :setka_layout_id

      t.datetime :published_at
      t.timestamps

      t.integer :parent_id, null: true, index: true
      t.integer :lft, null: false, index: true
      t.integer :rgt, null: false, index: true

      # optional fields
      t.integer :depth, null: false, default: 0
      t.integer :children_count, null: false, default: 0
    end
  end
end

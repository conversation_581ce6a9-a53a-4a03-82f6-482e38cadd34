version: "3.4"

services:
  server:
    image: nginx
    ports:
      - 8080:8080
    volumes:
      - "./nginx.conf:/etc/nginx/nginx.conf"
    links:
      - app
      - frontend

  frontend:
    build:
      context: ../intranet-portal-rit_frontend
    volumes:
      - ./nginx.front.conf:/etc/nginx/nginx.conf

  app:
    user: "${UID}:${GID}"
    tty: true
    stdin_open: true
    image: intranet/web
    build:
      context: .
      target: development
    env_file:
      - .env
      - .env.local
    volumes:
      - .:/intranet-portal
    command: bundle exec puma

  grafana:
    image: grafana/grafana
    ports:
      - 9500:3000

  prometheus:
    image: prom/prometheus
    ports:
      - 9090:9090
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  prometheus_exporter:
    build:
      context: .
      dockerfile: Metrics.Dockerfile
    ports:
      - 9400:9394

  db:
    image: postgres
    environment:
      - POSTGRES_USER=intranet
      - POSTGRES_PASSWORD=intranet
      - POSTGRES_DB=intranet
    ports:
      - 5432:5432
    volumes:
      - db:/var/lib/postgresql/data

  elasticsearch:
    image: elasticsearch:7.3.0
    ports:
      - 9200:9200
      - 9300:9300
    environment:
      - discovery.type=single-node

  redis:
    image: redis
    ports:
      - 6379:6379
    volumes:
      - cache:/data

  minio:
    image: minio/minio
    ports:
      -  9000:9000
    volumes:
      - minio:/data
    env_file:
      - .env
    command: ["server", "/data"]

  mailhog:
   image: mailhog/mailhog
   ports:
     - 1025:1025
     - 8025:8025

  sidekiq:
    env_file:
      - .env
      - .env.local
    image: intranet/web
    volumes:
      - .:/intranet-portal
    command: sidekiq -C config/sidekiq.yml

  emailer:
    image: intranet/web
    env_file:
      - .env
      - .env.local
    volumes:
      - .:/intranet-portal
    command: rails emailer:start

volumes:
  db:
  cache:
  minio:

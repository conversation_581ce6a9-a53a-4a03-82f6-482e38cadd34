version: "3.4"

services:
  db:
    networks:
      - my-i-nework
    image: postgres:10.10
    environment:
      - POSTGRES_USER=intranet
      - POSTGRES_PASSWORD=intranet
      - POSTGRES_DB=intranet
    ports:
      - 15432:5432
    volumes:
      - db10:/var/lib/postgresql/data

  elasticsearch:
    networks:
      - my-i-nework
    image: elasticsearch:7.3.0
    ports:
      - 19200:9200
      - 19300:9300
    environment:
      - discovery.type=single-node

  redis:
    networks:
      - my-i-nework
    image: redis
    ports:
      - 16379:6379
    volumes:
      - cache:/data

volumes:
  db10:
  cache:

networks:
  my-i-nework:
    name: my-shared-i-network

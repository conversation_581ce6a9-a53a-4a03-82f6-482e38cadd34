parameters:
  - name: APP_IMAGE
  - name: FEATURE_BRANCH
  - name: FRONTEND_SERVICE

kind: Template
apiVersion: v1
metadata:
  name: intranet-portal-rit-services-template
objects:
  - kind: DeploymentConfig
    apiVersion: apps.openshift.io/v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-app
      namespace: intranet-portal-rit
    spec:
      replicas: 1
      revisionHistoryLimit: 0
      strategy:
        type: Recreate
      selector:
        app: ${FEATURE_BRANCH}-rails-app
        deploymentconfig: ${FEATURE_BRANCH}-rails-app
      template:
        metadata:
          labels:
            app: ${FEATURE_BRANCH}-rails-app
            deploymentconfig: ${FEATURE_BRANCH}-rails-app
        spec:
          containers:
            - env:
                - name: PORT
                  valueFrom:
                    configMapKeyRef:
                      key: PORT
                      name: staging-rails
                - name: RAILS_MASTER_KEY
                  valueFrom:
                    configMapKeyRef:
                      key: RAILS_MASTER_KEY
                      name: staging-rails
                - name: POSTGRES_HOST
                  valueFrom:
                    configMapKeyRef:
                      key: POSTGRES_HOST
                      name: staging-rails
                - name: POSTGRES_DBNAME
                  valueFrom:
                    configMapKeyRef:
                      key: POSTGRES_DBNAME
                      name: staging-rails
                - name: POSTGRES_USER
                  valueFrom:
                    configMapKeyRef:
                      key: POSTGRES_USER
                      name: staging-rails
                - name: POSTGRES_PASSWORD
                  valueFrom:
                    configMapKeyRef:
                      key: POSTGRES_PASSWORD
                      name: staging-rails
                - name: SETKA_LICENSE_KEY
                  valueFrom:
                    configMapKeyRef:
                      key: SETKA_LICENSE_KEY
                      name: staging-rails
                - name: SETKA_PUBLIC_TOKEN
                  valueFrom:
                    configMapKeyRef:
                      key: SETKA_PUBLIC_TOKEN
                      name: staging-rails
                - name: SMTP_AUTH
                  valueFrom:
                    configMapKeyRef:
                      key: SMTP_AUTH
                      name: staging-rails
                - name: SMTP_HOST
                  valueFrom:
                    configMapKeyRef:
                      key: SMTP_HOST
                      name: staging-rails
                - name: SMTP_PORT
                  valueFrom:
                    configMapKeyRef:
                      key: SMTP_PORT
                      name: staging-rails
                - name: SMTP_USER
                  valueFrom:
                    configMapKeyRef:
                      key: SMTP_USER
                      name: staging-rails
                - name: SMTP_PASSWORD
                  valueFrom:
                    configMapKeyRef:
                      key: SMTP_PASSWORD
                      name: staging-rails
                - name: MINIO_BUCKET
                  valueFrom:
                    configMapKeyRef:
                      key: MINIO_BUCKET
                      name: staging-rails
                - name: MINIO_HOST
                  valueFrom:
                    configMapKeyRef:
                      key: MINIO_HOST
                      name: staging-rails
                - name: MINIO_ACCESS_KEY
                  valueFrom:
                    configMapKeyRef:
                      key: MINIO_ACCESS_KEY
                      name: staging-rails
                - name: MINIO_SECRET_KEY
                  valueFrom:
                    configMapKeyRef:
                      key: MINIO_SECRET_KEY
                      name: staging-rails
                - name: RAILS_LOG_TO_STDOUT
                  valueFrom:
                    configMapKeyRef:
                      key: RAILS_LOG_TO_STDOUT
                      name: staging-rails
                - name: RAILS_SERVE_STATIC_FILES
                  valueFrom:
                    configMapKeyRef:
                      key: RAILS_SERVE_STATIC_FILES
                      name: staging-rails
                - name: HOST
                  value: http://${FEATURE_BRANCH}.oc.video.rt.ru
                - name: FEATURE_HOST
                  value: http://${FEATURE_BRANCH}.oc.video.rt.ru
                - name: REDIS_HOST
                  valueFrom:
                    configMapKeyRef:
                      key: REDIS_HOST
                      name: staging-rails
                - name: ELASTICSEARCH_URL
                  valueFrom:
                    configMapKeyRef:
                      key: ELASTICSEARCH_URL
                      name: staging-rails
                - name: REDIS_PROVIDER
                  valueFrom:
                    configMapKeyRef:
                      key: REDIS_PROVIDER
                      name: staging-rails
                - name: MAIL_FROM
                  valueFrom:
                    configMapKeyRef:
                      key: MAIL_FROM
                      name: staging-rails
                - name: FEATURE_HOST
                  value: ${FEATURE_BRANCH}.oc.video.rt.ru
                - name: SMS_GATE_EXTERNAL_ID1
                  valueFrom:
                    configMapKeyRef:
                      key: SMS_GATE_EXTERNAL_ID1
                      name: staging-rails
                - name: SMS_GATE_EXTERNAL_ID3
                  valueFrom:
                    configMapKeyRef:
                      key: SMS_GATE_EXTERNAL_ID3
                      name: staging-rails
                - name: SMS_GATE_SEND_SMS_URL
                  valueFrom:
                    configMapKeyRef:
                      key: SMS_GATE_SEND_SMS_URL
                      name: staging-rails
                - name: EIP_PWD
                  valueFrom:
                    configMapKeyRef:
                      key: EIP_PWD
                      name: staging-rails
                - name: EIP_LOGIN
                  valueFrom:
                    configMapKeyRef:
                      key: EIP_LOGIN
                      name: staging-rails
                - name: SMS_TEMPLATE_ID
                  valueFrom:
                    configMapKeyRef:
                      key: SMS_TEMPLATE_ID
                      name: staging-rails
                - name: SENTRY_DSN
                  valueFrom:
                    configMapKeyRef:
                      key: SENTRY_DSN
                      name: staging-rails
                - name: CAFETERIA_HOST
                  valueFrom:
                    configMapKeyRef:
                      key: CAFETERIA_HOST
                      name: staging-rails
                - name: CAFETERIA_SECRET_TOKEN
                  valueFrom:
                    configMapKeyRef:
                      key: CAFETERIA_SECRET_TOKEN
                      name: staging-rails
                - name: VACATIONS_1C_FAKE
                  value: 'ON'
                - name: INTEGRATION_1C_AUTH_TOKEN
                  value: '686a61dadaac2cd64b81e82c1e4018c7'
                - name: EMAILER_AUTH_TOKEN
                  valueFrom:
                    configMapKeyRef:
                      key: EMAILER_AUTH_TOKEN
                      name: staging-rails
                - name: EMAILER_HOST
                  valueFrom:
                    configMapKeyRef:
                      key: EMAILER_HOST
                      name: staging-rails
                - name: CAFETERIA_HOST
                  valueFrom:
                    configMapKeyRef:
                      key: CAFETERIA_HOST
                      name: staging-rails
                - name: CAFETERIA_SECRET_TOKEN
                  valueFrom:
                    configMapKeyRef:
                      key: CAFETERIA_SECRET_TOKEN
                      name: staging-rails
              image: ${APP_IMAGE}
              imagePullPolicy: Always
              name: ${FEATURE_BRANCH}-rails-app
              ports:
                - containerPort: 8080
                  protocol: TCP
              resources:
                limits:
                  cpu: 500m
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
          dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
      test: false

  - kind: Service
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-app-service
      namespace: intranet-portal-rit
    spec:
      ports:
        - name: 8080-tcp
          port: 8080
          protocol: TCP
          targetPort: 8080
      selector:
        app: ${FEATURE_BRANCH}-rails-app
        deploymentconfig: ${FEATURE_BRANCH}-rails-app

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-front
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FRONTEND_SERVICE}
        weight: 100
    wildcardPolicy: None

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-admin
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /admin
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FEATURE_BRANCH}-rails-app-service
        weight: 100
    wildcardPolicy: None

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-api
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /api
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FEATURE_BRANCH}-rails-app-service
        weight: 100
      wildcardPolicy: None

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-assets
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /packs
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FEATURE_BRANCH}-rails-app-service
        weight: 100
      wildcardPolicy: None

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-setka
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /setka
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FEATURE_BRANCH}-rails-app-service
        weight: 100
      wildcardPolicy: None

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-storage
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /rails
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FEATURE_BRANCH}-rails-app-service
        weight: 100
      wildcardPolicy: None

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-swagger
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /swagger_doc
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FEATURE_BRANCH}-rails-app-service
        weight: 100
      wildcardPolicy: None

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-swagger-assets
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /assets
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FEATURE_BRANCH}-rails-app-service
        weight: 100
      wildcardPolicy: None

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-redirect
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /link
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FEATURE_BRANCH}-rails-app-service
        weight: 100
      wildcardPolicy: None

  - kind: Route
    apiVersion: v1
    metadata:
      labels:
        app: Backend-features
      name: ${FEATURE_BRANCH}-rails-cafeteria
      namespace: intranet-portal-rit
    spec:
      host: ${FEATURE_BRANCH}.oc.video.rt.ru
      path: /cafeteria
      port:
        targetPort: 8080-tcp
      to:
        kind: Service
        name: ${FEATURE_BRANCH}-rails-app-service
        weight: 100
    wildcardPolicy: None

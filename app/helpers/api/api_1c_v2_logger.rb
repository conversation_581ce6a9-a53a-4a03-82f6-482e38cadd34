# frozen_string_literal: true

class API::API1cV2Logger < API::API1cLogger
  private

  # rubocop:disable Metrics/PerceivedComplexity
  # rubocop:disable Metrics/CyclomaticComplexity
  def name
    if request_method == 'GET' && request_target.start_with?('/v2/1c/vacations')
      :get_vacation
    elsif request_method == 'POST' && request_target.start_with?('/v2/1c/vacations')
      :post_vacation
    elsif request_method == 'POST' && request_target.start_with?('/v2/1c/cancellation-status')
      :post_cancellation_status
    elsif request_method == 'POST' && request_target.match?(%r{v2\/1c\/verify\/vacations\/\d{5}\/in_case_of_timeout$})
      :verify_get_approved_vacations_timeout
    elsif request_method == 'POST' && request_target.start_with?('/v2/1c/verify')
      :verify_get_vacations
    else
      :get_add_count_of_vacations_day
    end
  end
  # rubocop:enable Metrics/PerceivedComplexity
  # rubocop:enable Metrics/CyclomaticComplexity
end

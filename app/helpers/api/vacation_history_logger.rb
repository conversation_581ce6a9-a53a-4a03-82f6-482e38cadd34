# frozen_string_literal: true

module API::VacationHistoryLogger
  def store_vacation_history(vacation, event, profile_id, confirmation_info = nil, created_at = DateTime.current)
    VacationHistory.create(
      vacation_period_id: vacation.id,
      status: vacation.status,
      whodunnit: profile_id,
      event: event,
      created_at: created_at,
      confirmation_info: confirmation_info,
      start_date: vacation.start_date,
      days: vacation.days,
      vacation_period_parent_id: vacation.parent_id
    )
  end
end

# frozen_string_literal: true

module API::VacationPlanningNotifyHelpers
  def notify_chief_that_employee_planned_vacations(employee_profile)
    return unless employee_profile.find_line_chief.present?

    VacationsPlanningMailer.with(chief: employee_profile.find_line_chief,
                                 employee: employee_profile,
                                 intersections: employee_profile.planning_intersections_with_other_employees)
                           .notify_chief_that_employee_planned_vacations
                           .deliver_later
  end

  def notify_employee_that_planned_vacations_agreed(employee_profiles)
    employee_profiles.each do |employee|
      next if employee.general_manager?

      VacationsPlanningMailer.with(employee: employee,
                                   planning_year: employee.current_date_in_tz.year.next)
                             .notify_employee_that_planned_vacations_agreed
                             .deliver_later
    end
  end

  def notify_employee_that_planned_vacations_rejected(chief_profile, employee_profile_id)
    employee = Profile.find(employee_profile_id)
    return unless employee.present?

    VacationsPlanningMailer.with(chief: chief_profile,
                                 employee: employee,
                                 planning_year: employee.current_date_in_tz.year.next)
                           .notify_employee_that_planned_vacations_rejected
                           .deliver_later
  end

  def notify_employee_that_chief_planned_vacations_for_him(chief_profile, employee_profile_id)
    employee = Profile.find(employee_profile_id)
    return unless employee.present?

    VacationsPlanningMailer.with(chief: chief_profile,
                                 employee: employee,
                                 planning_year: employee.current_date_in_tz.year.next)
                           .notify_employee_that_chief_planned_vacations_for_him
                           .deliver_later
  end

  def notify_employee_that_chief_changed_his_planned_vacations(chief_profile, employee_profile_id)
    employee = Profile.find(employee_profile_id)
    return unless employee.present?

    VacationsPlanningMailer.with(chief: chief_profile,
                                 employee: employee,
                                 planning_year: employee.current_date_in_tz.year.next)
                           .notify_employee_that_chief_changed_his_planned_vacations
                           .deliver_later
  end
end

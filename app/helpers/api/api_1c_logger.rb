# frozen_string_literal: true

class API::API1cLogger < Grape::Middleware::Base
  def before
    @time = Time.now
    @log_api_request = APIRequest.new
    @log_api_request.request_method = request_method
    @log_api_request.endpoint = request_target
    @log_api_request.remote_ip = request_ip
    @log_api_request.name = name
    @log_api_request.file.attach(attach_file_data(request_params)) unless @log_api_request.file.attached?
  end

  def after
    return if @app_response.nil?

    @log_api_request.response_code = response_status
    @log_api_request.runtime = Time.now - @time
    @log_api_request.file.attach(attach_file_data(response_body)) if request_method == 'GET'
    @log_api_request.save
    @tmp_file.close
  end

  private

  def response_status
    return if @app_response.nil?

    @app_response[0]
  end

  def response_headers
    @app_response[1]
  end

  def response_body
    parts = []
    @app_response[2].each { |part| parts << part }
    parts.join
  end

  def request_method
    env['REQUEST_METHOD']
  end

  def request_target
    env['PATH_INFO']
  end

  def request_ip
    env['REMOTE_ADDR']
  end

  def request_params
    env['rack.input'].read
  end

  def name
    if request_method == 'GET' && request_target == '/1c/vacations'
      :get_vacation
    elsif request_method == 'POST' && request_target == '/1c/vacations'
      :post_vacation
    elsif request_method == 'POST' && request_target == '/1c/cancellation-status'
      :post_cancellation_status
    else
      :get_add_count_of_vacations_day
    end
  end

  def attach_file_data(data)
    data = CGI.unescape(data)
    @tmp_file = Tempfile.new('file_data')
    @tmp_file << data
    @tmp_file.rewind
    ActiveStorage::Blob.create_after_upload!(
      io: @tmp_file, filename: "api_1c_data_#{Time.now.to_i}.json"
    )
  end
end

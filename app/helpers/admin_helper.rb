# frozen_string_literal: true

module AdminHelper
  include ActionView::Helpers::AssetTagHelper
  include Pundit

  ASSET_MATCH = {
    js: :javascript_include_tag,
    css: :stylesheet_link_tag
  }.freeze

  def asset_tag(asset_info)
    send(
      ASSET_MATCH[asset_info[:type]],
      asset_info[:path]
    )
  end

  def admin_email
    current_user.email.gsub(/@.*/, '')
  end

  def admin_role
    current_user.role.name
  end

  def allowed_settings_access?
    [
      policy(:tag_edit).show?,
      policy(:city_and_office_edit).edit?,
      policy(Role).all?,
      policy(:setting).edit?
    ].any?(true)
  end

  def policy_menu_item(model, action, name, url, link_options = {})
    return unless policy(model).send "#{action}?"

    link_to name, url, link_options
  end

  def auth_policy_menu_item(post, model, action, name, url, link_options = {})
    return unless model.new(current_user, post).send "#{action}?"

    link_to name, url, link_options
  end

  def edit_date(edit_ts)
    if (Time.now.to_date - edit_ts.to_date).to_i < 7
      local_time(edit_ts, format: '%d.%m.%y, %H:%M')
    else
      local_time(edit_ts, format: '%d.%m.%y')
    end
  end
end

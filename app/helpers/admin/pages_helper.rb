# frozen_string_literal: true

module Admin
  module PagesHelper
    include SharedHelper

    def categories_for_select
      Category.all.map { |category| [category.title, category.id] }
    end

    def categories_for_filter
      every_cat = [t('admin.pages.all_category'), nil]
      [every_cat] + categories_for_select
    end

    def page_statuses_for_filter
      statuses = Page.statuses.map { |status, id| [Page.human_enum_name(:status, status), id] }

      [['Все статусы', nil]] + statuses
    end
  end
end

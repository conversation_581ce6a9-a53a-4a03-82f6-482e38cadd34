# frozen_string_literal: true

module Admin
  module TagsHelper
    include Shared<PERSON>elper

    def profiles_with_tag(tag)
      decorated_profiles = String.new
      Profile.actual.eager_load(:account, :assignable_tags_to_posts, :assignable_tags_to_profiles).where(id: tag.profiles.ids).each do |profile|
        as_tag_to_profile = profile.assignable_tags_to_profiles.select { |as_tag| as_tag.tag_id == tag.id }.present?
        as_tag_to_post = profile.assignable_tags_to_posts.select { |as_tag| as_tag.tag_id == tag.id }.present?
        can_edit_tag = TagEditPolicy.new(profile.account, nil).edit? if profile.account.present?
        if as_tag_to_profile || as_tag_to_post || can_edit_tag
          decorated_user = UserDecorator.new(profile)
          decorated_profiles.blank? ? decorated_profiles.concat(decorated_user.full_name) : decorated_profiles.concat(", #{decorated_user.full_name}")
        end
      end
      decorated_profiles
    end

    def sort_column
      %w[assigned_tags posts title].include?(params[:sort]) ? params[:sort] : 'assigned_tags'
    end

    def employees_with_tag(tag)
      AssignedTag.where(tag: tag)
                 .left_joins(:profile)
                 .merge(Profile.actual)
                 .select('distinct on (profile_id) *')
                 .size
    end
  end
end

# frozen_string_literal: true

module Admin
  module PostsHelper
    include SharedHelper

    def icon(icon, title = nil)
      raw %(<span title="#{title}"><i class="#{icon}"></i> #{title}</span>)
    end

    def fa_icon(icon, title = nil)
      raw %(<span> #{title} <i class="#{icon}" aria-hidden="true"></i></span>)
    end

    def post_statuses_for_filter
      statuses = Post.statuses.map { |status, id| [Post.human_enum_name(:status, status), id] }

      [['Все статусы', nil]] + statuses
    end

    def cities_for_filter
      cities = City.all.map { |city| [city.name, city.id] }
      [['Для всех городов', 'all']] + cities
    end

    def tags_for_filter
      tags = @tags.map { |tag| [tag.title, tag.id] }
      [['Новости без тегов', 'without']] + tags
    end
  end
end

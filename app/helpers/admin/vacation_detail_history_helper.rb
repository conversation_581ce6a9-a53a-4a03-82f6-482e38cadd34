# frozen_string_literal: true

module Admin
  module VacationDetailHistoryHelper
    def parent_vacation_from_history(vacation, history)
      history.find { |hist| hist.vacation_period_parent_id && hist.vacation_period_parent_id != vacation.id }
    end

    def child_vacations_ids_from_history(vacation)
      VacationHistory.where(vacation_period_parent_id: vacation.id)
                     .pluck(:vacation_period_id)
                     .uniq
    end
  end
end

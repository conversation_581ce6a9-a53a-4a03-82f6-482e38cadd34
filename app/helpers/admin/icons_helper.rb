# frozen_string_literal: true

module Admin
  module IconsHelper
    def fa_icon(icon, title = nil)
      raw %(<span> #{title} <i class="#{icon}" aria-hidden="true"></i></span>)
    end

    def icon(icon, title = nil)
      raw %(<span title="#{title}"><i class="#{icon}"></i> #{title}</span>)
    end

    def popup_icon(icon, title)
      raw %(<span title="#{title}"><i class="#{icon}"></i></span>)
    end
  end
end

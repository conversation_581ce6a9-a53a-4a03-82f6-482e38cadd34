# frozen_string_literal: true

module Admin
  module Consultants<PERSON><PERSON>per
    def consultants_profile_list
      Profile.actual
             .order(:last_name)
             .map { |p| ["#{p.last_name} #{p.first_name} (#{p.email})", p.id] }
    end

    def move_consultatations_modal(consultant_id)
      button = button_tag(type: 'button', class: 'btn btn-primary btn-sm', data: { toggle: 'modal', target: "#modal-#{consultant_id}" }) do
        popup_icon 'fas fa-trash-alt', t('to_deactivate')
      end
      popup = render partial: 'admin/components/popup_form', locals: move_consultatations_modal_locals(consultant_id)
      popup + button
    end

    def move_consultatations_modal_locals(consultant_id)
      {
        title: t('admin.career_consultations.consultant.to_deactivate'),
        body: move_consultatations_select_tmpl(consultant_id),
        action_url: reassign_consultations_admin_consultant_path(consultant_id),
        id: "modal-#{consultant_id}"
      }
    end

    def move_consultatations_select_tmpl(consultant_id)
      select_options = Consultant.where
                                 .not(id: consultant_id, status: :disabled)
                                 .collect { |consultant| [UserDecorator.new(consultant.profile).name_with_email, consultant.id] }
      capture do
        concat content_tag(:p, t('admin.career_consultations.tmpl.intro'))
        concat select_tag('new-consultant', options_for_select(select_options))
      end
    end
  end
end

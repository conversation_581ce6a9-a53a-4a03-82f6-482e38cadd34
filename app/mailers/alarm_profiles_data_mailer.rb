# frozen_string_literal: true

class AlarmProfilesDataMailer < ApplicationMailer
  def duplicates_by_employee_number_notify
    fname = DateTime.now.strftime("%Y.%m.%d %H:%M #{I18n.t('extractables.alarm_profiles_data_mailer.duplicates_by_employee_number.file_name')}")
    attachments[fname] = { mime_type: 'text/csv', content: params[:attachment] }

    mail(to: PORTAL_EMAIL, subject: I18n.t('extractables.alarm_profiles_data_mailer.duplicates_by_employee_number.subject'))
  end

  def profiles_without_chief_notify
    fname = DateTime.now.strftime("%Y.%m.%d %H:%M #{I18n.t('extractables.alarm_profiles_data_mailer.profiles_without_chief.file_name')}")
    attachments[fname] = { mime_type: 'text/csv', content: params[:attachment] }

    mail(to: PORTAL_EMAIL, subject: I18n.t('extractables.alarm_profiles_data_mailer.profiles_without_chief.subject'))
  end

  def profiles_without_employee_number_notify
    fname = DateTime.now.strftime("%Y.%m.%d %H:%M #{I18n.t('extractables.alarm_profiles_data_mailer.profiles_without_employee_number.file_name')}")
    attachments[fname] = { mime_type: 'text/csv', content: params[:attachment] }

    mail(to: PORTAL_EMAIL, subject: I18n.t('extractables.alarm_profiles_data_mailer.profiles_without_employee_number.subject'))
  end
end

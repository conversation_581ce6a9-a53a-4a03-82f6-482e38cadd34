# frozen_string_literal: true

module EmailerService
  class CareerConsultationsMailer
    attr_reader :consultation

    def initialize(consultation)
      @consultation = consultation
    end

    def notify_consultant_about_new_consultation
      employee = consultation.profile
      consultant = consultation.consultant
      template_variables = {
        consultantFirstName: consultant.profile.first_name,
        employeeFirstAndLastName: "#{employee.first_name} #{employee.last_name}",
        pageUrl: "#{ENV['HOST']}/profile/#{employee.id}",
        requestUrl: "#{ENV['HOST']}/career-counseling/#{consultation.id}"
      }

      template_name = EMPLOYEE_NEW_REQUEST_FOR_CREATE_CONSULTATION
      send_email(template_name, template_variables, I18n.t('mailer.new-request-for-create-consultation.subject'), consultant.profile.email)
    end

    def notify_employee_about_new_meeting(meeting)
      employee = consultation.profile

      template_name = CAREER_CONSELING_MEETING
      send_email(template_name, employee_templete_variables_about_meeting(meeting), I18n.t('mailer.new-consultation-meeting.subject'), employee.email)
    end

    def notify_employee_about_change_meeting(meeting)
      employee = consultation.profile

      template_name = CAREER_CONSULTING_MEETING_CHANGE
      send_email(template_name, employee_templete_variables_about_meeting(meeting),
                 I18n.t('mailer.change-consultation-meeting.subject'), employee.email)
    end

    def notify_employee_about_cancel_meeting(meeting)
      employee = consultation.profile

      template_name = CAREER_CONSULTING_MEETING_CANCEL
      send_email(template_name, employee_templete_variables(meeting), I18n.t('mailer.cancel-consultation-meeting.subject'), employee.email)
    end

    def notify_employee_about_feedback(meeting)
      employee = consultation.profile

      template_variables = {
        employeeFirstName: employee.first_name,
        consultationDate: meeting_time(meeting),
        requestUrl: "#{ENV['HOST']}/career-counseling/#{consultation.id}"
      }

      template_name = CAREER_CONSULTING_MEETING_FEEDBACK
      send_email(template_name, template_variables, I18n.t('mailer.feedback-consultation-meeting.subject'), employee.email)
    end

    def notify_consultant_for_asking_result
      employee = consultation.profile
      consultant = consultation.consultant
      template_variables = {
        consultantFirstName: consultant.profile.first_name,
        email: employee.email,
        requestUrl: "#{ENV['HOST']}/career-counseling/#{consultation.id}"
      }

      template_name = CAREER_CONSULTING_ASLING_RESULT
      send_email(template_name, template_variables, I18n.t('mailer.consultant-asking-for-result.subject'), consultant.profile.email)
    end

    private

    def employee_templete_variables_about_meeting(meeting)
      employee = consultation.profile
      consultant = consultation.consultant

      {
        employeeFirstName: employee.first_name,
        consultationPlaceUrl: meeting.meeting_place,
        consultationDate: meeting_time(meeting),
        email: consultant.profile.email,
        requestUrl: "#{ENV['HOST']}/career-counseling/#{consultation.id}"
      }
    end

    def employee_templete_variables(meeting)
      employee = consultation.profile
      consultant = consultation.consultant

      {
        employeeFirstName: employee.first_name,
        consultationDate: meeting_time(meeting),
        email: consultant.profile.email,
        requestUrl: "#{ENV['HOST']}/career-counseling/#{consultation.id}"
      }
    end

    def meeting_time(meeting)
      time_zone = consultation.profile.office&.city&.time_zone

      if time_zone.blank?
        "#{I18n.l(meeting.meeting_date.in_time_zone('Europe/Moscow'), format: '%d %B в %H:%M')} (по МСК)"
      else
        I18n.l(meeting.meeting_date.in_time_zone(time_zone), format: '%d %B в %H:%M')
      end
    end

    def send_email(template_name, template_variables, subject, to)
      return if template_variables.blank?

      Emailer.send(template_name: template_name,
                   to: to,
                   subject: subject,
                   template_variables: template_variables,
                   priority: :medium)
    end
  end
end

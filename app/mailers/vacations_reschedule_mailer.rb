# frozen_string_literal: true

class VacationsRescheduleMailer < ApplicationMailer
  def notify_employee_about_inactive_reschedule
    attach_social_files
    attach_vacation_files

    @employee_name = params[:employee].first_name
    @vacation_dates = "#{I18n.l(params[:vacation].start_date, format: '%e %B')} — #{I18n.l(params[:vacation].end_date, format: '%e %B %Y')}"
    @vacation_reschedule_link = "#{ENV['HOST']}/vacation?vacation=#{params[:vacation].id}&step=transfer"

    store_vacation_history(params[:vacation], :notify_employee_about_inactive_reschedule, params[:employee].id, params[:employee].email)

    mail(to: params[:employee].email, subject: 'Требуется перенос отпуска')
  end

  def notify_chief_about_coming_reschedule
    attach_social_files
    attach_vacation_files
    @image_src['alert.png'] = base64_image_src('icons/alert.png')

    @is_line_chief = params[:is_line_chief]
    @chief_name = params[:chief].first_name
    @employee_name = "#{params[:employee].last_name} #{params[:employee].first_name}"

    @vacation_dates = "#{I18n.l(params[:vacation].start_date, format: '%e %B')} — #{I18n.l(params[:vacation].end_date, format: '%e %B %Y')}"
    @vacation_intersections = params[:intersections]
    @vacation_details_link = "#{ENV['HOST']}/staff-vacations?transfer-vacation=#{params[:vacation].id}"
    @vacation_confirmation_link = "#{@vacation_details_link}&step=confirmation&fromEmail=true"

    mail(to: params[:chief].email, subject: 'Срочно согласовать отпуск')
  end

  private

  def attach_vacation_files
    @image_src['logo_small.png'] = base64_image_src('logo_small.png')
    @image_src['footer_vacation.png'] = base64_image_src('footer_vacation.png')
    @image_src['calendar.png'] = base64_image_src('icons/calendar.png')
  end

  def store_vacation_history(vacation, event, profile_id, confirmation_info = nil, created_at = DateTime.current)
    VacationHistory.create(
      vacation_period_id: vacation.id,
      status: vacation.status,
      whodunnit: profile_id,
      event: event,
      created_at: created_at,
      confirmation_info: confirmation_info,
      start_date: vacation.start_date,
      days: vacation.days
    )
  end
end

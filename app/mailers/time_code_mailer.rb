# frozen_string_literal: true

class TimeCodeMailer < ApplicationMailer
  layout 'designed_mailer'

  def notify
    @image_src['logo_small.png'] = base64_image_src('logo_small.png')
    @image_src['footer_bird_postman.png'] = base64_image_src('footer_bird_postman.png')
    attachments.inline['font.woff'] = attach_file('BasisGrotesquePro-Regular.woff')

    email = params[:email]
    @code = params[:code].strip
    @content = I18n.t('mailer.code-generated-mailer.content', verification_action_name: verification_action_name)
    subject = I18n.t('mailer.code-generated-mailer.subject', verification_action_name: verification_action_name)
    mail(to: email, subject: subject)
  end

  def verification_action_name
    if params[:action_name] == :verify_phone
      'телефона'
    elsif params[:action_name] == :vacation_confirmation
      'действия на портале'
    end
  end
end

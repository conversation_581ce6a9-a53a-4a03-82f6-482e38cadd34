# frozen_string_literal: true

class UpcomingBirthdayMailer < ApplicationMailer
  layout 'designed_mailer'

  def notify
    @image_src['logo_small.png'] = base64_image_src('logo_small.png')
    attachments.inline['font.woff'] = attach_file('BasisGrotesquePro-Regular.woff')
    @image_src['footer_birthday_full.png'] = base64_image_src('footer_birthday_full.png')

    @name = params[:subscriber].first_name

    @birthday_name = "#{params[:subscribed_on].first_name} #{params[:subscribed_on].last_name}"
    @date = I18n.l(Date.today.advance(days: DAYS_TILL_BIRTHDAY_NOTIFY), format: '%e %B, %A')
    @person_profile_link = "#{ENV['HOST']}/profile/#{params[:subscribed_on].id}"

    mail(to: params[:subscriber].email, subject: 'Напоминание о дне рождения')
  end
end

# frozen_string_literal: true

class CancelledEventMailer < ApplicationMailer
  layout 'designed_mailer'

  def notify_about_cancellation
    @image_src['logo.png'] = base64_image_src('logo_small.png')
    @image_src['footer_event.png'] = base64_image_src('footer_bird_postman.png')
    attachments.inline['font.woff'] = attach_file('BasisGrotesquePro-Regular.woff')
    user = params[:user]
    email = user.email
    @event = params[:event]
    @name = user.first_name
    @content = "Мероприятие «#{@event.title}» отменяется"
    @link = "#{ENV['HOST']}/events"
    @label = 'Другие мероприятия'
    mail(to: email, subject: "Мероприятие «#{@event.title}» отменяется")
  end
end

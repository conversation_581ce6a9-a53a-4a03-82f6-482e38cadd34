# frozen_string_literal: true

module TelegramBot
  class OrderDocumentMailer < ApplicationMailer
    def send_order
      @document_type = if params[:order][:document_type] == I18n.t('bot.ndfl_document')
                         params[:order][:document_type][0] = params[:order][:document_type][0].downcase
                         params[:order][:document_type]
                       else
                         params[:order][:document_type].downcase
                       end

      @period = params[:order][:period]
      @number_of_copies = params[:order][:number_of_copies]
      @address = params[:order][:address]
      @phone = params[:order][:phone]

      @user_name = "#{params[:user][:last_name]} #{params[:user][:first_name]}"
      @user_email = params[:user][:email]

      mail(to: ENV['HR_SPRAVKI_EMAIL'], subject: I18n.t('bot.mailer.order_document.subject'))
    end
  end
end

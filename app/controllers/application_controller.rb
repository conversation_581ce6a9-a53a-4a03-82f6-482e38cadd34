# frozen_string_literal: true

class ApplicationController < ActionController::Base
  before_action :authenticate_user!
  skip_before_action :verify_authenticity_token
  before_action :set_paper_trail_whodunnit

  protected

  def authenticate_user!
    if user_signed_in?
      super
    else
      redirect_to '/'
    end
  end

  def set_paper_trail_whodunnit
    PaperTrail.request.whodunnit = if current_user.present?
                                     current_user.email
                                   else
                                     'Анонимный пользователь'
                                   end
  end
end

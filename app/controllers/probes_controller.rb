# frozen_string_literal: true

class ProbesController < ActionController::Base
  def live
    render plain: 'OK',
           status: 200,
           layout: false
  end

  def ready
    if db_ready?
      render plain: 'OK',
             status: 200
    else
      render plain: 'NOT READY',
             status: 423, # :locked
             layout: false
    end
  end

  private

  def db_ready?
    ActiveRecord::Base.connected?
  end
end

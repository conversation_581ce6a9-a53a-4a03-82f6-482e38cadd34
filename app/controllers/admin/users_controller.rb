# frozen_string_literal: true

module Admin
  class UsersController < AdminController
    include ActionView::Helpers::<PERSON>rlHelper

    before_action only: %i[show edit new_account] do
      @user = Admin::UsersService.user_by_id(params[:id])
    end

    def index
      @q = Admin::UsersService.filter_query(params[:q])
      @users = Admin::UsersService.all_users(query: @q, page: params[:page])
    end

    def show; end

    def edit; end

    def update
      Admin::UsersService.update_by_id(params[:id], profile_params)

      flash[:notice] = [I18n.t('admin.users.update.success')]

      redirect_back(fallback_location: admin_root_path)
    rescue ActiveRecord::RecordInvalid => e
      show_sync_validation_errors(e)
    end

    private

    def profile_params
      params.require(:user_profile)
            .permit(Admin::UsersService.all_fields)
    end
  end
end

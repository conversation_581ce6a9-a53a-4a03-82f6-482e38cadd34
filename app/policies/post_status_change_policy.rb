# frozen_string_literal: true

class PostStatusChangePolicy < PostPolicy
  def update?(params)
    !check_current_policy && params.key?(:status)
  end

  def edit?
    check_current_policy && check_belongs_to_city
  end

  def hide?
    check_current_policy && record.published?
  end

  def send_off_moderation?
    check_current_policy && record.on_moderation?
  end

  def publish?
    check_current_policy && check_belongs_to_city && !record.published?
  end

  def index?
    check_current_policy && check_belongs_to_city
  end

  class Scope < Scope
    def statuses
      status_array = Post.statuses.to_a

      published_status_index = 1
      scheduled_status_index = 3
      status_array.delete_at(scheduled_status_index) if scope.published? && scope.published_at <= DateTime.now
      unless check_current_policy
        status_array.delete_at(scheduled_status_index)
        status_array.delete_at(published_status_index)
      end

      status_array.to_h
    end
  end

  private

  def check_belongs_to_city
    !account.profile.office&.city&.id.blank?
  end
end

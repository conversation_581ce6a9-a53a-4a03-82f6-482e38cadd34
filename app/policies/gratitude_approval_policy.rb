# frozen_string_literal: true

class GratitudeApprovalPolicy < ModelPolicy
  def can_approve?
    under_limit = record.profile.number_of_gratitude_approvals_given_this_month < GRATITUDE_APPROVALS_PER_MONTH_LIMIT
    under_limit && GratitudeApproval.where(profile: record.profile, gratitude: record).empty?
  end

  def can_remove_approval?
    record && Date.today < record.created_at.end_of_month
  end
end

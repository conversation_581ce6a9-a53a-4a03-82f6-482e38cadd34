# frozen_string_literal: true

class PostEditOnlyCityPolicy < ModelPolicy
  def missing_methods
    super.push(:show?, :create?)
  end

  def edit?
    check_current_policy && check_city && draft?
  end

  def delete?
    check_current_policy && check_city && !record.published?
  end

  class Scope < Scope
    def resolve
      city_id = account.profile&.office&.city.id unless account.profile.office&.city&.id.blank?

      return super if city_id.nil? || !check_current_policy

      scope.includes(:cities).where(cities: { id: city_id })
    end
  end

  private

  def check_city
    return false if account.profile.office&.city&.id.blank?

    return true if record.blank?

    record.cities.map(&:id).include?(account.profile&.office&.city.id)
  end

  def not_published?
    record.status != 'published'
  end

  def draft?
    return true if record.blank?

    record.draft?
  end
end

# frozen_string_literal: true

class PostEditAllCitiesPolicy < ModelPolicy
  def missing_methods
    super.push(:show?, :create?)
  end

  def edit?
    check_current_policy && draft?
  end

  def delete?
    check_current_policy && !record.published?
  end

  class Scope < Scope
    def resolve
      return super unless check_current_policy

      scope.all
    end
  end

  private

  def draft?
    return true if record.blank?

    record.draft?
  end
end

# frozen_string_literal: true

class EventDecorator < SimpleDelegator
  def published_date
    return I18n.l(published_at, format: :short) unless published_at.nil?

    '-'
  end

  def scheduled_date
    return I18n.l(scheduled_at, format: :short) unless scheduled_at.nil?

    '-'
  end

  def human_read_status
    Event.human_enum_name(:status, status)
  end

  def human_read_category
    Event.human_enum_name(:category, category)
  end

  def human_read_cities
    return I18n.t('admin.events.for_all_cities') if cities.empty?

    cities.map(&:name).join(', ')
  end
end

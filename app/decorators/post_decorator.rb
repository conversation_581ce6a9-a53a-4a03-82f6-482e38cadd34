# frozen_string_literal: true

class PostDecorator < SimpleDelegator
  def published_date
    return I18n.l(published_at, format: :short) unless published_at.nil?

    '-'
  end

  def human_read_status
    return Post.human_enum_name(:status, 'scheduled') if status == 'published' && published_at > DateTime.now

    Post.human_enum_name(:status, status)
  end

  def human_read_cities
    return I18n.t('admin.posts.for_all_cities') if cities.empty?

    cities.map(&:name).join(', ')
  end
end

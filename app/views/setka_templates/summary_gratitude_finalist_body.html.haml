%h1.stk-reset.stk-theme_39107__style_font_style-1588079267523.stk-theme_39107__mb_0{"data-ce-tag" => "paragraph", "data-stk-css" => "stkxAAPQ"} Итоги конкурса «Человек месяца»
%p.stk-reset.stk-theme_39107__mb_0{"data-ce-tag" => "paragraph", "data-stk-css" => "stkhr0Zu"} Надеемся, все заметили, что последние полгода в корпоративных сетях витает дух благодарностей друг к другу. ☺️ Каждый месяц мы подводим итоги конкурса «Человек месяца», рейтинги составлены на основе количества полученных «Спасибо» сотрудниками на портале.
%p.stk-reset.stk-theme_39107__mb_0{"data-ce-tag" => "paragraph", "data-stk-css" => "stkyGV8K"}= "С момента запуска этого проекта вы отправили #{total_gratitudes} благодарностей коллегам — похоже на кармическое волшебство, правда? 😌 Давайте посмотрим на финалиста каждого месяца и поздравим лайком к этой новости."
%p.stk-reset.stk-theme_39107__mb_0{"data-ce-tag" => "paragraph", "data-stk-css" => "stk3tf53"} Мы рады разделять такую корпоративную культуру, где отмечается помощь, взаимовыручка, экспертность и всё то, за что вы говорите спасибо своим напарникам, команде, лидам.
.stk-grid{"data-ce-tag" => "grid", "data-stk-css" => "stk0mT27"}
  - finalists_with_avatar_url.each do |finalist, avatar_url|
    - if avatar_url
      .stk-grid-col.valign-middle.align-left{"data-ce-tag" => "grid-col", "data-col-width" => "2", "data-stk-css" => "stkiYPai"}
        %figure.stk-reset.logoEventWrapper.logoEventNews.stk-image-figure{"data-ce-tag" => "image-figure"}
          .stk-mask.leftLogoMask{"data-ce-tag" => "mask"}
            %img.stk-image.stk-reset{"data-image-id" => "18316", "data-image-name" => "avatar.png", :height => "480", :src => "#{avatar_url}", :width => "480"}/

    .stk-grid-col{"data-ce-tag" => "grid-col", "data-col-width" => "2", "data-stk-css" => "stkJI3cD"}
      %p.stk-reset.pink-block{"data-ce-tag" => "paragraph", "data-stk-css" => "stkCHo3q"}= "Человек #{I18n.l(finalist.honour_date, format: '%B')}"
      - profile = finalist.profile
      - user_decorator = UserDecorator.new(profile)
      %h3.stk-reset.stk-theme_39107__style_font_style-1588079187870.stk-theme_39107__mb_0{"data-ce-tag" => "paragraph", "data-stk-css" => "stklc6V5"}= "#{user_decorator.full_name}"
      %p.stk-reset.stk-theme_39107__style_font_style-1588079083733.stk-theme_39107__mb_0{"data-ce-tag" => "paragraph", "data-stk-css" => "stkSYu92"}= "#{profile.position}"

%style{"data-stk-css" => "stkNXZrm", :media => "all"}
  [data-stk-css="stkNXZrm"]:not(#stk):not(#stk):not(style) {
  max-width: 650px
  }
%style{"data-stk-css" => "stkLTwoe", :media => "all"}
  [data-stk-css="stkLTwoe"]:not(#stk):not(#stk):not(style) {
  max-width: 650px
  }
%style{"data-stk-css" => "stk0mT27", :media => "all"}
  [data-stk-css="stk0mT27"]:not(#stk):not(#stk):not(style) {
  max-width: 650px
  }
%style{"data-stk-css" => "stkb-PEX", :media => "all"}
  [data-stk-css="stkb-PEX"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stkiYPai", :media => "all"}
  [data-stk-css="stkiYPai"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stkzTzSR", :media => "all"}
  [data-stk-css="stkzTzSR"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stkJI3cD", :media => "all"}
  [data-stk-css="stkJI3cD"]:not(#stk):not(#stk):not(style) {
  margin-left: 15px
  }
%style{"data-stk-css" => "stkhr0Zu", :media => "all"}
  [data-stk-css="stkhr0Zu"]:not(#stk):not(#stk):not(style) {
  max-width: 650px;
  padding-left: 12px;
  margin-bottom: 16px
  }
%style{"data-stk-css" => "stkxAAPQ", :media => "all"}
  [data-stk-css="stkxAAPQ"]:not(#stk):not(#stk):not(style) {
  padding-left: 12px;
  margin-bottom: 20px
  }
%style{"data-stk-css" => "stkyGV8K", :media => "all"}
  [data-stk-css="stkyGV8K"]:not(#stk):not(#stk):not(style) {
  max-width: 650px;
  padding-left: 12px;
  margin-bottom: 16px
  }
%style{"data-stk-css" => "stk3tf53", :media => "all"}
  [data-stk-css="stk3tf53"]:not(#stk):not(#stk):not(style) {
  max-width: 650px;
  padding-left: 12px;
  margin-bottom: 40px
  }
%style{"data-stk-css" => "stk4MCyx", :media => "all"}
  [data-stk-css="stk4MCyx"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stkyxlfI", :media => "all"}
  [data-stk-css="stkyxlfI"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkSYu92", :media => "all"}
  [data-stk-css="stkSYu92"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stklc6V5", :media => "all"}
  [data-stk-css="stklc6V5"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkCHo3q", :media => "all"}
  [data-stk-css="stkCHo3q"]:not(#stk):not(#stk):not(style) {
  font-size: 16px;
  font-weight: bold;
  color: white;
  padding: 8px 12px;
  background-color: #E376E0;
  width: max-content;
  border-radius: 8px
  }
%style{"data-stk-css" => "stkNfbSU", :media => "all"}
  [data-stk-css="stkNfbSU"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stkS-5FT", :media => "all"}
  [data-stk-css="stkS-5FT"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkFqEoM", :media => "all"}
  [data-stk-css="stkFqEoM"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stkHxlJS", :media => "all"}
  [data-stk-css="stkHxlJS"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stkS7-w8", :media => "all"}
  [data-stk-css="stkS7-w8"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stkKJEw-", :media => "all"}
  [data-stk-css="stkKJEw-"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkxkEsA", :media => "all"}
  [data-stk-css="stkxkEsA"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stk3QNmD", :media => "all"}
  [data-stk-css="stk3QNmD"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stkftP3H", :media => "all"}
  [data-stk-css="stkftP3H"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stkF5O9v", :media => "all"}
  [data-stk-css="stkF5O9v"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkWybBm", :media => "all"}
  [data-stk-css="stkWybBm"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stk_xxMb", :media => "all"}
  [data-stk-css="stk_xxMb"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stkEFiVL", :media => "all"}
  [data-stk-css="stkEFiVL"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stk7hiyx", :media => "all"}
  [data-stk-css="stk7hiyx"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkfeooR", :media => "all"}
  [data-stk-css="stkfeooR"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stk17MkV", :media => "all"}
  [data-stk-css="stk17MkV"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stkfmb7c", :media => "all"}
  [data-stk-css="stkfmb7c"]:not(#stk):not(#stk):not(style) {
  max-width: 650px
  }
%style{"data-stk-css" => "stk0GmIb", :media => "all"}
  [data-stk-css="stk0GmIb"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stkrrDmF", :media => "all"}
  [data-stk-css="stkrrDmF"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stknbY0e", :media => "all"}
  [data-stk-css="stknbY0e"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stkz-rpX", :media => "all"}
  [data-stk-css="stkz-rpX"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stk9_fF7", :media => "all"}
  [data-stk-css="stk9_fF7"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stkwwUO7", :media => "all"}
  [data-stk-css="stkwwUO7"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkUX1ff", :media => "all"}
  [data-stk-css="stkUX1ff"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stk5Khxx", :media => "all"}
  [data-stk-css="stk5Khxx"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stk_qsHK", :media => "all"}
  [data-stk-css="stk_qsHK"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stkznCJ0", :media => "all"}
  [data-stk-css="stkznCJ0"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkKkQ67", :media => "all"}
  [data-stk-css="stkKkQ67"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stkZ_Q3E", :media => "all"}
  [data-stk-css="stkZ_Q3E"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stkn1NWa", :media => "all"}
  [data-stk-css="stkn1NWa"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stkgp7kY", :media => "all"}
  [data-stk-css="stkgp7kY"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stktX3sb", :media => "all"}
  [data-stk-css="stktX3sb"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stkKFQI-", :media => "all"}
  [data-stk-css="stkKFQI-"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stk8UrzE", :media => "all"}
  [data-stk-css="stk8UrzE"]:not(#stk):not(#stk):not(style) {
  max-width: 650px
  }
%style{"data-stk-css" => "stkBMxZf", :media => "all"}
  [data-stk-css="stkBMxZf"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stkcXtJ5", :media => "all"}
  [data-stk-css="stkcXtJ5"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkfSMpt", :media => "all"}
  [data-stk-css="stkfSMpt"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stkGFeZ_", :media => "all"}
  [data-stk-css="stkGFeZ_"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stkS7Qlm", :media => "all"}
  [data-stk-css="stkS7Qlm"]:not(#stk):not(#stk):not(style) {
  font-size: 14px;
  margin-bottom: 32px;
  color: #797E8B
  }
%style{"data-stk-css" => "stk5XMD_", :media => "all"}
  [data-stk-css="stk5XMD_"]:not(#stk):not(#stk):not(style) {
  margin-bottom: 8px
  }
%style{"data-stk-css" => "stkFjvWg", :media => "all"}
  [data-stk-css="stkFjvWg"]:not(#stk):not(#stk):not(style) {
  margin-left: -15px
  }
%style{"data-stk-css" => "stkaGHBy", :media => "all"}
  [data-stk-css="stkaGHBy"]:not(#stk):not(#stk):not(style) {
  margin-left: 12px
  }
%style{"data-stk-css" => "stk3TANL", :media => "all"}
  [data-stk-css="stk3TANL"]:not(#stk):not(#stk):not(style) {
  max-width: 650px
  }

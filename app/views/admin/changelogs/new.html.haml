= link_to icon('fas fa-arrow-left', t('admin.changelogs.title')), admin_changelogs_path, class: 'btn btn-link'

%h1= t('admin.changelogs.add')

= simple_form_for Changelog.new, url: admin_changelogs_path, html: { class: 'js-form-changelog' } do |f|
  = f.hidden_field :image_meta_key, value: @image_meta_key
  = f.input :title, label: t('admin.changelogs.theme')
  .js-stk-editor.stk-editor#setka-editor.mt-4{ "data-editor-config": @setka.editor_config, "data-token": @setka.public_token, "data-theme-id": CHANGELOG_SETKA_TEMPLATE_ID, "data-layout-id": CHANGELOG_SETKA_LAYOUT_ID, "data-body": @changelog_body, "data-image-meta-key": @image_meta_key }
  = f.button :submit, t('to_save'), class: 'btn-primary mt-4'

= link_to icon('fas fa-arrow-left', t('admin.vacations.title')), admin_vacation_logs_path, class: 'btn btn-link'

%h2.mt-2= t('admin.vacation_histories.general_manager.title')

= simple_form_for :general_manager, url: change_general_manager_admin_vacation_logs_path, class: 'mb-4' do |f|
  = f.input :chief_number, include_blank: false, collection: @tab_num_list, selected: @chief_number, label_method: :employee_number, value_method: :id, input_html: { class: 'js-general-manager', multiple: 'multiple' }, include_hidden: false, label: t('admin.vacation_histories.general_manager.employee_number_list')
  .d-flex.justify-content-between.mt-4
    = f.submit t('to_save'), class: 'btn btn-primary'
= link_to icon('fas fa-arrow-left', t('admin.vacation_histories.title')), for_portal_vacations_admin_vacation_logs_path, class: 'btn btn-link'

%h2.mt-2= @profile.full_name
= search_form_for @q, url: profile_vacations_list_admin_vacation_log_path, class: 'mb-4' do |f|
  .form-group
    = f.label :updated_at
    #date_search_field_update.input-group.date{"data-target-input" => "nearest"}
      %input.form-control.datetimepicker-input{"data-target" => "#date_search_field_update", :type => "text",:name => "q[updated_at_eq]", :value => @updated_at_eq}/
      .input-group-append{"data-target" => "#date_search_field_update", "data-toggle" => "datetimepicker"}
        .input-group-text
          %i.fa.fa-calendar
  .form-group
    = f.label :start_date
    #date_search_field_create.input-group.date{"data-target-input" => "nearest"}
      %input.form-control.datetimepicker-input{"data-target" => "#date_search_field_create", :type => "text",:name => "q[start_date_eq]", :value => @start_date_eq}/
      .input-group-append{"data-target" => "#date_search_field_create", "data-toggle" => "datetimepicker"}
        .input-group-text
          %i.fa.fa-calendar
  .form-group
    = f.label :status
    = f.select :status_eq, vacation_periods_status_for_filter, {}, { class: 'form-control' }
  .form-group
    = f.label :id
    = f.search_field :id_eq, class: 'form-control'
  .d-flex.justify-content-between.mt-4
    = f.submit t('to_apply'), class: 'btn btn-primary'
    = link_to t('to_clear_search'), request.path, class:"cancel-button"

.container
  .row
    .col #{t('activerecord.attributes.profile.employee_number')}: #{@profile.employee_number}
    .col #{t('activerecord.attributes.profile.available_vacation_days')}: #{@profile.available_vacation_days}
    .w-100
    .col #{t('activerecord.attributes.profile.email')}: #{@profile.email}
    .col #{t('activerecord.attributes.profile.available_vacation_types')}: #{@profile.available_vacation_types}
    .w-100
    .col #{t('activerecord.attributes.profile.employments.enterprise')}: #{@profile.employments.map { |e| e.enterprise_name }.join(', ')}
    .w-100
    .col #{t('activerecord.attributes.profile.employments.asg_name')}: #{@profile.employments.map { |e| e.asg_name }.join(', ')}

-if @vacations.present?
  %table.table.mt-5
    %thead
      %tr
        %th= t('vacation_detail_history.table.vacation_id')
        %th= t('vacation_detail_history.table.vacation_type')
        %th= sortable 'start_date', t('vacation_detail_history.table.start_date')
        %th= t('vacation_detail_history.table.days')
        %th= t('vacation_detail_history.table.current_status')
        %th= t('vacation_detail_history.table.updated_at_date')
        %th
    %tbody
      -@decorated_vacations.each do |vacation|
        %tr
          %td= vacation.id
          %td= vacation.human_read_type
          %td= I18n.l(vacation.start_date.in_time_zone('Europe/Moscow'), format: '%d.%m.%Y').to_s
          %td= vacation.days
          %td= vacation.human_read_status
          %td= vacation.updated_date
          %td= link_to t('in_detail'), vacation_detail_history_admin_vacation_log_path(vacation.id),class: 'btn btn-secondary'
  = will_paginate @vacations, renderer: WillPaginate::ActionView::BootstrapLinkRenderer
-else
  %h2.mt-2= t('no_search_results')

:javascript
  $(function () {
      $('#date_search_field_update').datetimepicker({
          format: 'L'
      });

      $('#date_search_field_create').datetimepicker({
          format: 'L'
      });
  });

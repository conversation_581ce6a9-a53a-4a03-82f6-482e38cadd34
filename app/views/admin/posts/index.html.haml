%h1= t('admin.posts.list.title')

- create_button_title = t('to_create', resource: t('admin.posts.item').downcase)

%h2.mt-2 Фильтр
= search_form_for @q, url: admin_posts_path, class: 'mb-4' do |f|
  .form-group
    = f.label :title
    = f.search_field :title_cont, class: 'form-control'
  .form-group
    - if policy(:post_edit_all_cities).create?
      = f.label :city
      = f.select :cities_children_of, cities_for_filter, {}, { class: 'js-cities-select form-control', multiple: 'multiple'}
  .form-group
    = f.label :status
    = f.select :status_eq, post_statuses_for_filter, {}, { class: 'form-control' }
  .form-group
    = f.label :is_pinned
    = f.select :is_pinned, [['Все', nil], ['Закрепленная', 1]], {}, { class: 'form-control' }
  .form-group
    - if policy(:tag_edit).add_tags_to_post?
      = f.label :tag_ids
      = f.select :tags_children_of, tags_for_filter, {}, { class: 'js-tags-select form-control', multiple: 'multiple'}
  .d-flex.justify-content-between.mt-4
    = f.submit 'Применить', class: 'btn btn-primary'
    = link_to icon('far fa-file', create_button_title), new_admin_post_path, class: 'btn btn-primary'

%hr

-if @posts.present?
  %table.table.mt-5
    %thead
      %tr
        %th= sortable 'is_pinned', 'Закрепленная'
        %th ID
        %th= sortable 'title', 'Заголовок'
        %th Город
        %th Дата редактирования
        %th Статус
        %th
    %tbody
      -@decorated_posts.each do |post|
        %tr
          %td
            - if post.is_pinned?
              .badge.badge-success 'Закрепленная'
          %td= post.id
          %td= link_to truncate(post.title, length: 100), admin_post_path(post.id)
          %td= post.human_read_cities
          %td= edit_date(post.updated_at)
          %td
            - if post.draft?
              - badge_class = 'badge-warning'
            - elsif post.published?
              - if post.published_at <= DateTime.now
                - badge_class = 'badge-success'
              -else
                - badge_class = 'badge-secondary'
            - elsif post.on_moderation?
              - badge_class = 'badge-info'
            .d-flex.flex-column.align-items-start
              .badge(class=badge_class)= post.human_read_status
              - if post.published?
                .mt-1.fs-14
                  = local_time(post.published_at, :short)
          %td
            .btn-group.btn-group-sm
              = auth_policy_menu_item post, PostPolicy, :edit, icon('far fa-edit'), edit_admin_post_path(post.id), { class: 'btn btn-primary btn-sm', "data-toggle": "tooltip", title: t('to_start_editing') }
              = auth_policy_menu_item post, PostStatusChangePolicy, :publish, icon('far fa-check-circle'), publish_admin_post_path(post.id), { class: 'btn btn-primary btn-sm', data: { method: :patch }, "data-toggle": "tooltip", title: t('to_publish') }
              = auth_policy_menu_item post, PostPublishFromCompanyPolicy, :publish, icon('fas fa-building'), publish_from_corporation_admin_post_path(post.id), { class: 'btn btn-primary btn-sm', data: { method: :patch }, "data-toggle": "tooltip", title: t('to_publish_from_corporation') }
              %button.btn.btn-primary.dropdown-toggle.dropdown-toggle-split{"data-toggle": "dropdown"}
              .dropdown-menu
                = link_to icon('fas fa-file', t('in_detail')), admin_post_path(post.id), class: 'dropdown-item'
                = auth_policy_menu_item post, PostPolicy, :moderation, icon('fas fa-user-check', t('admin.posts.send_on_moderation')), on_moderation_admin_post_path(post.id), { class: 'dropdown-item', data: { method: :patch } }
                = auth_policy_menu_item post, PostStatusChangePolicy, :hide, icon('fas fa-eye-slash', t('to_hide')), hide_admin_post_path(post.id), { class: 'dropdown-item', data: { method: :patch } }
                = auth_policy_menu_item post, PostPolicy, :hide, icon('fas fa-eye-slash', t('admin.posts.send_off_moderation')), hide_admin_post_path(post.id), { class: 'dropdown-item', data: { method: :patch } }
                = auth_policy_menu_item post, PostStatusChangePolicy, :publish, icon('fas fa-calendar-check', t('to_schedule')), edit_admin_post_path(post, post: {status: 'scheduled'}), { class: 'dropdown-item' }
                .dropdown-divider
                = auth_policy_menu_item post, PostPolicy, :delete, icon('fas fa-trash-alt', t('to_delete')), admin_post_path(post.id), { class: 'dropdown-item', data: { method: :delete, confirm: t('are_you_sure') } }
  = will_paginate @posts, renderer: WillPaginate::ActionView::BootstrapLinkRenderer

-else
  %h2.mt-2= t('no_search_results')

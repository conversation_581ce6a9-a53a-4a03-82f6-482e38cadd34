= link_to icon('fas fa-arrow-left', t('admin.posts.list.title')), admin_posts_path, class: 'btn btn-link'

%h1 Добавить новость

= simple_form_for Post.new, url: admin_posts_path, html: { class: 'js-form-post' } do |f|
  = f.hidden_field :image_meta_key, value: @image_meta_key
  = f.input :title
  - if policy(:post_edit_all_cities).create?
    = f.input :city_ids, include_blank: false, collection: @cities, label_method: :name, value_method: :id, input_html: { class: 'js-posts-cities', multiple: 'multiple' }, include_hidden: false
  - if policy(:tag_edit).add_tags_to_post?
    = f.input :tag_ids, include_blank: false, collection: @tags, label_method: :title, value_method: :id, input_html: { class: 'js-posts-tags', multiple: 'multiple' }, include_hidden: false
  .js-stk-editor.stk-editor#setka-editor.mt-4{ "data-editor-config": @setka.editor_config, "data-token": @setka.public_token,  "data-image-meta-key": @image_meta_key }
  = f.button :submit, t('to_save'), class: 'btn-primary mt-4'
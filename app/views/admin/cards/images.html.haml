= link_to icon('fas fa-arrow-left', t('admin.cards.title')), admin_cards_path, class: 'btn btn-link'

%h1= t 'admin.cards.images'

= hidden_field_tag 'proxy_url', upload_file_proxy_admin_cards_path

= simple_form_for @card, url: admin_cards_path do |f|
  .form-group
    .form-controls.mt-4.ml-3
      = f.hidden_field :direct_upload_url, value: direct_upload_files_to_card_admin_card_path(@card.id)
      %div{"data-uppy" => "images[]"}
        = link_to "#", data: { behavior: "uppy-trigger"}, class: 'btn btn-primary', role: "button" do
          = icon "fas fa-cloud-upload-alt", t('admin.cards.download')
        = f.button :submit, class: 'submit-uppy', :style => "display: none;"
  - if @card.images.any?
    - @card.images.each do |img|
      %tr
        %td
          %tr
            .form-group
              .form-controls.mt-4
                %td
                  = image_tag url_for(img), class: 'w-25'
                %td
                  = link_to destroy_image_admin_card_path(img.id), class: 'btn btn-danger ml-4', data: { method: :delete } do
                    = icon "fa fa-trash", t('admin.cards.delete')

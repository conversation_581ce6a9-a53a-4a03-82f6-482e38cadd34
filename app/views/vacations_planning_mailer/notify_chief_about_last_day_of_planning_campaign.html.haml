!!!
%html{:lang => "ru"}
  %head
    %meta{:charset => "UTF-8"}/
    %meta{:content => "light dark", :name => "color-scheme"}/
    %meta{:content => "light dark", :name => "supported-color-schemes"}/
    %title vacation/ScheduleConfirmLastDay
  %body
    %table{:align => "center", :bgcolor => "#272f3d", :border => "0", :cellpadding => "0", :cellspacing => "0", :height => "100%", :style => "width: 600px;", :valign => "top", :width => "600px"}
      %tbody
        %tr
          %td{:align => "center", :valign => "top"}
            %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :style => "padding: 0px 60px;", :valign => "top"}
              %tbody
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td{:style => "height: 50px; background: none; font-size: 1px; margin: 0px; border: none;"}  
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td
                            %img{:alt => "logo", :height => "auto", :src => @image_src['logo_small.png'], :style => "border: none;", :width => "24"}/
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td{:style => "height: 40px; background: none; font-size: 1px; margin: 0px; border: none;"}  
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td
                            %span{:style => "font-family: Arial, Ubuntu, Helvetica, sans-serif; font-weight: bold; line-height: 48px; font-size: 42px; color: #ffffff;"} #{@chief_name}, привет!
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td{:style => "height: 16px; background: none; font-size: 1px; margin: 0px; border: none;"}  
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td
                            %span{:style => "font-family: Arial, Ubuntu, Helvetica, sans-serif; font-weight: normal; font-size: 24px; line-height: 32px; color: #ffffff; letter-spacing: 0.02em;"} Сегодня последний день для согласования графиков отпусков сотрудников.
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td{:style => "height: 40px; background: none; font-size: 1px; margin: 0px; border: none;"}  
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr{:style => "border: none;"}
                          %td{:style => "background-color: #8348fc; border-radius: 8px; padding: 12px 24px;"}
                            %a{:href => "#{@details_link}", :style => "color: #ffffff; text-decoration: none; font-family: Arial, Ubuntu, Helvetica, sans-serif; font-size: 16px; line-height: 24px; font-weight: bold;", :target => "_blank"} Подробнее
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td{:style => "height: 80px; background: none; font-size: 1px; margin: 0px; border: none;"}  
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td
                            %img{:alt => "footer", :height => "auto", :src => @image_src['footer_vacation.png'], :style => "border: none;", :width => "480"}/
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td{:style => "height: 40px; background: none; font-size: 1px; margin: 0px; border: none;"}  
                %tr
                  %td{:align => "center"}
                    %span{:style => "font-family: Arial, Ubuntu, Helvetica, sans-serif; font-weight: normal; font-size: 16px; line-height: 24px; color: #d3d3d3;"} ПОДПИСЫВАЙСЯ НА НАШИ СОЦСЕТИ
                    %table{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td
                            %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                              %tbody
                                %tr
                                  %td{:style => "height: 25px; background: none; font-size: 1px; margin: 0px; border: none;"}  
                        %tr
                          %td
                            %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                              %tbody
                                %tr
                                  %td
                                    %a{:href => TELEGRAM_LINK, :style => "text-decoration: none;", :target => "_blank"}
                                      %img{:height => "auto", :src => @image_src['telegram.png'], :style => "border: none;", :width => "26"}/
                                      %span{:style => "font-family: Arial, Ubuntu, Helvetica, sans-serif; font-weight: normal; font-size: 16px; line-height: 24px; color: #ffffff; margin-left: 16px; vertical-align: middle;"}
                                  %td{:style => "padding-left: 30px;"}
                                    %a{:href => VK_LINK, :style => "text-decoration: none;", :target => "_blank"}
                                      %img{:height => "auto", :src => @image_src['vk.png'], :style => "border: none;", :width => "26"}/
                                      %span{:style => "font-family: Arial, Ubuntu, Helvetica, sans-serif; font-weight: normal; font-size: 16px; line-height: 24px; color: #ffffff; margin-left: 16px; vertical-align: middle;"}
                        %tr
                          %td
                            %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                              %tbody
                                %tr
                                  %td{:style => "height: 20px; background: none; font-size: 1px; margin: 0px; border: none;"}  
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td{:style => "height: 20px; background: none; font-size: 1px; margin: 0px; border: none;"}  
        %tr
          %td{:style => "background: #1d2533; padding: 0px 86px;"}
            %table{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
              %tbody
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td{:style => "height: 20px; background: none; font-size: 1px; margin: 0px; border: none;"}  
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr{:style => "text-align: center;"}
                          %td
                            %span{:style => "font-family: Arial, Ubuntu, Helvetica, sans-serif; font-weight: normal; font-size: 16px; line-height: 24px; color: #ffffff;"} Если что-то пошло не так, обратись
                        %tr
                          %td
                            %span{:style => "font-family: Arial, Ubuntu, Helvetica, sans-serif; font-weight: normal; font-size: 16px; line-height: 24px; color: #ffffff;"}
                              в службу поддержки на 
                              %a{:href => "mailto:<EMAIL>", :style => "color: #ffffff; text-decoration: none;", :target => "_blank"} <EMAIL>
                %tr
                  %td
                    %table{:align => "left", :border => "0", :cellpadding => "0", :cellspacing => "0", :valign => "top"}
                      %tbody
                        %tr
                          %td{:style => "height: 20px; background: none; font-size: 1px; margin: 0px; border: none;"}  

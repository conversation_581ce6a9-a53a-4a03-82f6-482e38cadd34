# frozen_string_literal: true

class PersonOfTheYearGratitudesNotifyJob < ApplicationJob
  def perform(*_args)
    profiles = Profile.actual.includes(:person_of_the_year_gratitude).where.not(person_of_the_year_gratitudes: { id: nil })

    profiles.find_each(batch_size: 100).with_index do |profile, index|
      CustomsMailer.with(first_name: profile.first_name,
                         email: profile.email,
                         gratitudes: profile.person_of_the_year_gratitude.gratitudes)
                   .person_of_the_year_gratitudes_email
                   .deliver_later

      sleep(DELAY_BETWEEN_EMAILS_BATCH_SECONDS) if (index.next % 5).zero?
    end
  end
end

# frozen_string_literal: true

class FreeSpaceEventNotifyJob < ApplicationJob
  def perform(remind, remind_need_destroy: false)
    FreeSpaceEventMailer.with(
      event: remind.event,
      user: remind.profile
    ).notify_about_free_space.deliver_later
    return remind.destroy if remind_need_destroy && remind.profile.telegram_id.blank?

    telegram_msg = I18n.t 'mailer.free_space_event_remind.subject',
                          deep_interpolation: true,
                          theme: remind.event.title
    telegram_msg += "\n #{I18n.t('mailer.free_space_event_remind.message')}"
    TelegramBot::Notifier.send_notification(
      remind.profile,
      telegram_msg,
      reply_markup: {
        inline_keyboard: [
          [
            {
              text: I18n.t('mailer.free_space_event_remind.label'),
              callback_data: "event_register:#{remind.event.id}"
            }
          ]
        ]
      }
    )
  end
end

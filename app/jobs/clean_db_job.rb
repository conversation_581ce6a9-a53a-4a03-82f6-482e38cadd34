# frozen_string_literal: true

class CleanDbJob < ApplicationJob
  def perform
    objects_for_deletion = 0
    history = PaperTrail::Version.where('created_at >= ?', 18.months.ago).where('created_at <= ?', 12.months.ago)
    objects_for_deletion += history.count
    history.delete_all
    ApplicationRecord.connection.execute('VACUUM FULL versions')
    puts "Deleted #{objects_for_deletion} objects"
  end
end

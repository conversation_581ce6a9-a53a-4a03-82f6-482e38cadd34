# frozen_string_literal: true

class WrongChiefJob < ApplicationJob
  def perform
    periods.map do |period|
      chief = period.assigned_vacation_chiefs.where(escalated: false, is_line_chief: true, replacement_schedule_id: nil).first
      chief.update(chief_id: period.profile.parent_id)
      period
    end
  end

  def periods
    VacationPeriod.joins(%i[profile assigned_vacation_chiefs])
                  .where('start_date > ?', Date.current)
                  .where('assigned_vacation_chiefs.escalated = FALSE')
                  .where('assigned_vacation_chiefs.is_line_chief = TRUE')
                  .where('assigned_vacation_chiefs.replacement_schedule_id IS NULL')
                  .where('profiles.parent_id != assigned_vacation_chiefs.chief_id')
  end
end

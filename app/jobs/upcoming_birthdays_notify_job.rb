# frozen_string_literal: true

class UpcomingBirthdaysNotifyJob < ApplicationJob
  include Timezone<PERSON>ommon

  def perform(*_args)
    suitable_zones = find_suitable_timezones(ENV['DAY_HOUR_OF_BIRTHDAY_NOTIFY'])
    where_clause = suitable_zones.include?('Moscow') ? loose_time_zoned_clause : strict_time_zoned_clause

    not_notified_reminders = BirthdayReminder
                             .joins(:subscribed_on)
                             .includes([subscriber: [office: :city]], :subscribed_on)
                             .where(where_clause, Date.today.day, Date.today.month, suitable_zones)

    not_notified_reminders.find_each do |reminder|
      today_birthday_reminder(reminder)
    end

    birth_day = DAYS_TILL_BIRTHDAY_NOTIFY.days.from_now

    not_notified_reminders = BirthdayReminder
                             .joins(:subscribed_on)
                             .includes([subscriber: [office: :city]], :subscribed_on)
                             .where(where_clause, birth_day.day, birth_day.month, suitable_zones)

    not_notified_reminders.find_each do |reminder|
      upcoming_birthday_reminder(reminder)
    end
  rescue StandardError => e
    Sentry.capture_exception(e)
  end

  private

  def strict_time_zoned_clause
    <<-SQL
      profiles.id = birthday_reminders.subscribed_on_id
        and
      (profiles.fire_date IS NULL OR profiles.fire_date >= '#{Time.now.in_time_zone('Moscow').to_date}')
        and
      profiles.birth_date is not null
        and
      profiles.birthday_visible is true
        and
      extract(day from profiles.birth_date) = ?
        and
      extract(month from profiles.birth_date) = ?
        and
      #{default_cities_clause}
    SQL
  end

  def loose_time_zoned_clause
    <<-SQL
      profiles.id = birthday_reminders.subscribed_on_id
        and
      (profiles.fire_date IS NULL OR profiles.fire_date >= '#{Time.now.in_time_zone('Moscow').to_date}')
        and
      profiles.birth_date is not null
        and
      profiles.birthday_visible is true
        and
      extract(day from profiles.birth_date) = ?
        and
      extract(month from profiles.birth_date) = ?
        and
      #{loose_cities_clause}
    SQL
  end

  def today_birthday_reminder(reminder)
    # ActiveRecord путает сущности и письма отправляются тому у кого день рождения
    # Причина скорее всего в сложном селекте
    subscribed_on = Profile.find_by(id: reminder.subscribed_on_id)
    subscriber = Profile.find_by(id: reminder.subscriber_id)
    return unless subscriber && subscribed_on

    TodayBirthdayMailer
      .with(subscribed_on: subscribed_on, subscriber: subscriber)
      .notify
      .deliver_later

    birthday_person = subscribed_on
    notification_msg = I18n.t 'bot.today_birthday_remind',
                              deep_interpolation: true,
                              date: I18n.l(birthday_person.birth_date, format: '%-d %B'),
                              person: birthday_person_name(birthday_person)
    TelegramBot::Notifier.send_notification(
      subscriber,
      notification_msg
    )
  end

  def upcoming_birthday_reminder(reminder)
    subscribed_on = Profile.find_by(id: reminder.subscribed_on_id)
    subscriber = Profile.find_by(id: reminder.subscriber_id)
    return unless subscriber && subscribed_on

    UpcomingBirthdayMailer
      .with(subscribed_on: subscribed_on, subscriber: subscriber)
      .notify
      .deliver_later

    notification_msg = I18n.t 'bot.birthday_remind',
                              deep_interpolation: true,
                              person: birthday_person_name(subscribed_on),
                              days_till_birthday: DAYS_TILL_BIRTHDAY_NOTIFY.to_s
    TelegramBot::Notifier.send_notification(
      subscriber,
      notification_msg
    )
  end

  def birthday_person_name(person)
    "#{person.first_name} #{person.last_name}"
  end
end

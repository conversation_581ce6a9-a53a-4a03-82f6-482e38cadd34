# frozen_string_literal: true

class GratitudeFinalist<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> < ApplicationJob
  def perform(honour_date = nil)
    honour_date ||= Date.today.prev_month
    winner_values = GratitudeFinalistPointsCalculator.run(honour_date, filter_existing: true)
                                                     .group_by { |_, points| points }
                                                     .max_by { |points, _| points }
    return if winner_values.nil?

    winner_values.last.each do |value|
      GratitudeFinalist.create!(
        profile_id: value.first,
        honour_date: honour_date
      )
    end
  end
end

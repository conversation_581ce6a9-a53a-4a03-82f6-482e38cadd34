# frozen_string_literal: true

class VerifyPost1cV2VacationsJob < ApplicationJob
  def perform(data, enterprise_id)
    check_session_status(data['session_status'])

    get_integration_errors(data['integration_errors'])

    file_path = form_excel(get_integration_errors(data['integration_errors']), enterprise_id)

    Verification1cErrorsMailer.with(file_path: file_path).integration_errors.deliver_now

    FileUtils.rm_f(file_path)
  end

  private

  def form_excel(errors, enterprise_id)
    dir = "#{Rails.root}/storage"
    FileUtils.mkdir_p(dir) unless File.exist?(dir)
    column_names = ['Табельный сотрудника', 'Дата начала отпуска', 'Причина ошибки', 'Операция']
    file_name = "Ошибки-интеграции-с-1с-#{Date.today.strftime('%Y-%m-%d')}-#{enterprise_id[:enterprise_id]}.xlsx"
    Axlsx::Package.new do |page|
      wb = page.workbook
      wb.add_worksheet(name: 'Отчет') do |sheet|
        sheet.add_row column_names
        errors.each do |e|
          sheet.add_row e
          Rails.logger.debug(sheet)
        end
      end
      page.serialize("#{Rails.root}/storage/#{file_name}")
    end
    "#{Rails.root}/storage/#{file_name}"
  end

  def check_session_status(session_status)
    session_status.each do |status|
      Verification1cErrorsMailer.with(reason: status['reason']).not_ok_status_detected.deliver_now if status['status'] != 'Ok'
    end
  end

  def get_integration_errors(integration_errors_array)
    integration_errors = []

    integration_errors_array.each do |error|
      error['vacations'].each do |v|
        integration_errors << [error['employee_number'], v['start_date'], v['reason'], v['операция']]
      end
    end
    integration_errors
  end
end

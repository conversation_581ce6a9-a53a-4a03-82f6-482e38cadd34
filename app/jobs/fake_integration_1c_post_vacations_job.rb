# frozen_string_literal: true

class FakeIntegration1cPostVacationsJob < ApplicationJob
  # TODO: вернуть в продакшине
  queue_as :no_fail_allowed
  sidekiq_options retry: 0

  before_perform do |job|
    data = job.arguments.first
    @ids = if data.nil? || data.key?(:ids) == false
             nil
           else
             data[:ids]
           end
  end

  def perform(*_args)
    return unless VACATIONS_1C_FAKE == 'ON' && Setting.find_by_name(CODE_BY_FAKE_VACATION_SETTING).mode?

    FakeIntegration1cPrediction.new(@ids).to_process_emulation
  end
end

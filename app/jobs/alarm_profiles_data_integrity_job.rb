# frozen_string_literal: true

class AlarmProfilesDataIntegrityJob < ApplicationJob
  include ProfilesCsvExporter

  def perform(*_args)
    check_for_duplicate_employee_numbers
    check_for_empty_chief
    check_for_empty_employee_number
  end

  private

  def check_for_duplicate_employee_numbers
    duplicate_employee_numbers = Profile.actual.select(:employee_number)
                                        .where.not(employee_number: nil)
                                        .group(:employee_number)
                                        .having('count(*) > 1')
                                        .pluck(:employee_number)

    return if duplicate_employee_numbers.blank?

    profiles = Profile.actual.where(employee_number: duplicate_employee_numbers)
    attachment = export_profiles_to_csv(profiles)

    AlarmProfilesDataMailer.with(attachment: attachment).duplicates_by_employee_number_notify.deliver_later
  end

  def check_for_empty_chief
    profiles_without_chief = Profile.actual.where(parent_id: nil).reject(&:general_manager?)

    return if profiles_without_chief.blank?

    attachment = export_profiles_to_csv(profiles_without_chief)

    AlarmProfilesDataMailer.with(attachment: attachment).profiles_without_chief_notify.deliver_later
  end

  def check_for_empty_employee_number
    profiles_without_employee_number = Profile.actual.where(employee_number: nil)

    return if profiles_without_employee_number.blank?

    attachment = export_profiles_to_csv(profiles_without_employee_number)

    AlarmProfilesDataMailer.with(attachment: attachment).profiles_without_employee_number_notify.deliver_later
  end
end

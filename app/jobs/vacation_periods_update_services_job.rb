# frozen_string_literal: true

class VacationPeriodsUpdateServicesJob < ApplicationJob
  include DefineVacationStatus
  include API::VacationScheduleHelpers
  include Error::VacationPeriodsUpdateServicesErrorsHandler

  class ProcessingError < StandardError; end

  # TODO: при разработке на фичеветках комментируем строку 7 и 8. На продакшин возвращаем.
  queue_as :no_fail_allowed
  sidekiq_options retry: 0

  attr_reader :enterprise_id

  before_perform do |job|
    Rails.logger.info 'START VacationPeriodsUpdateJob before perform'
    AlarmVacationMailer.wrong_data_structure(JSON.pretty_generate(job.arguments.first)).deliver_later unless required_keys_valid?(job.arguments.first)
    @total_employee = job.arguments.first.collect { |obj| obj['employee_number'] }
    @profiles_days_before = []
    check_profiles_available_days(job.arguments.first)
    check_vacations_to_be_used
    klean_logs
  end

  after_perform do |_job|
    Rails.logger.info 'START VacationPeriodsUpdateJob after perform'
    check_available_days_after
    unless @profiles_with_wrong_days.blank?
      AlarmVacationMailer.diff_value_in_days_employees(JSON.pretty_generate(@profiles_with_wrong_days)).deliver_later
    end
    AlarmVacationMailer.duplicates_in_dates_alarm(JSON.pretty_generate(@duplicates)).deliver_later unless @duplicates.blank?
    employee_diff = (@total_employee - @total_processed_employee)
    AlarmVacationMailer.diff_count_in_sent_employees(employee_diff.join(', ')).deliver_later unless employee_diff.blank?
  end

  # rubocop:disable Metrics/MethodLength
  # rubocop:disable Metrics/PerceivedComplexity
  def perform(profiles_data, enterprise_id: nil)
    @enterprise_id = enterprise_id
    Rails.logger.info 'START VacationPeriodsUpdateJob perform'
    @duplicates = {}
    @total_processed_employee = []
    # rubocop:disable Metrics/BlockLength
    profiles_data.in_groups_of(100, false) do |profiles_group|
      profiles_group.each do |profile_data|
        item_employee_number_key_present?(profile_data)
        item_employee_number_present?(profile_data)

        profile = Profile.actual.find_by(employee_number: profile_data['employee_number'].strip)
        employment = Employment.find_by(employee_number: profile_data['employee_number'].strip)
        update_employment_days(profile_data, employment)
        employee_profile_present?(profile)

        @total_processed_employee << profile_data['employee_number']
        update_profile_days(profile_data, profile)
        vacations_data = profile_data['vacations'] || []
        item_vacations_present?(vacations_data, profile_data['employee_number'])

        # Добавил архивирование отпусков в таймлайне, если нет их в 1с.
        if vacations_data.none?
          need_to_archive(profile_data, profile)
          next
        end

        vacations_duplicates_present?(profile_data['employee_number'], vacations_data)

        vacations_data.each do |vacation_data|
          vacation_data['source_type'] = VacationPeriod.source_types[:exchange_1c]
          vacation = VacationPeriod.order(id: :asc)
                                   .find_by(profile: profile, start_date: vacation_data['start_date'])

          # Если те же даты
          same_dates = vacation&.end_date.to_s.eql?(vacation_data['start_date'])
          # Если перенос оформлен через кадры
          parent = vacation.parent if same_dates && vacation.on_approval? && vacation.parent&.on_reschedule?
          # в таком случае у дочернего отпуска chief_number => nil
          # и не корректно происходит поиск руководителя
          vacation.chief_number = parent.chief_number if parent && vacation.chief_number.nil?
          integration_1c_service_call(vacation_data, vacation, profile)
        rescue StandardError => e
          handle_errors(e, profile_data)
        end

        all_vacations_archived?(profile)
        # нужно убрать отпуска в статусе новый, которых нет в данных с 1c
        need_to_archive(profile_data, profile)
      rescue StandardError => e
        handle_errors(e, profile_data)
      end
    end
    # rubocop:enable Metrics/BlockLength
  end
  # rubocop:enable Metrics/MethodLength
  # rubocop:enable Metrics/PerceivedComplexity

  private

  def required_keys_valid?(data)
    data.each do |item|
      return false unless item.key?('available_vacation_days') && item.key?('vacations') && item.key?('employee_number')
    end
  end

  def check_vacations_to_be_used
    vacations = VacationPeriod.left_joins(:profile).merge(Profile.actual)
                              .where(status: :transferred_as_payment, source_type: :exchange_1c)

    vacations.each do |vacation|
      vacation.used! if (vacation.end_date - Time.now.in_time_zone('Moscow').to_date).to_i.negative?
    rescue StandardError => e
      log_exception(e)
      Sentry.set_extras(vacation_id: vacation.id)
      Sentry.capture_exception(e)
    end
  end

  def check_profiles_available_days(vacations_data)
    vacations_data.each do |vacation_data|
      @profiles_days_before << { vacation_data['employee_number'] => {
        sent: vacation_data['available_vacation_days'],
        was: Profile.find_by_employee_number(vacation_data['employee_number'])&.available_vacation_days
      } }
    end
  end

  def check_available_days_after
    @profiles_with_wrong_days = []
    @profiles_days_before.each do |obj|
      processed_profile_days = Profile.find_by(employee_number: obj.keys.first.to_s)
      next if processed_profile_days.nil?

      next if obj.values.first[:sent].to_i == processed_profile_days.available_vacation_days && number?(obj.values.first[:sent])

      @profiles_with_wrong_days << { obj.keys.first.to_sym =>
                                         { sent: obj.values.first[:sent],
                                           saved: processed_profile_days.available_vacation_days,
                                           was: obj.values.first[:was] } }
    end
  end

  def number?(string)
    true if Float(string)
  rescue StandardError
    false
  end

  def update_profile_days(item, profile)
    profile.available_vacation_days = item.key?('available_vacation_days') ? item['available_vacation_days'] : profile.available_vacation_days
    if item.key?('available_vacation_days_details') && item['available_vacation_days_details']
      profile.available_vacation_days_details = item['available_vacation_days_details']
    end

    change_vacation_type_keys(profile)

    profile.have_schedule = item.key?('has_schedule') ? item['has_schedule'] : profile.have_schedule
    profile.save!
  rescue StandardError => e
    log_exception(e)
    Sentry.set_extras(profile_id: profile.id, item: item)
    Sentry.capture_exception(e)
  end

  def change_vacation_type_keys(profile)
    return unless enterprise_id.to_i == Employment::ENTERPRISE_RTK_IT_PLUS

    profile.available_vacation_days_details.each do |time_period|
      next if time_period.last.blank?

      hash = profile.available_vacation_days_details[time_period.first]['types']
      hash.keys.each { |k| hash[VacationPeriod::TYPE_MAPPING[k]] = hash.delete(k) if VacationPeriod::TYPE_MAPPING[k] }
      profile.available_vacation_days_details[time_period.first]['types'] = hash
    rescue StandardError => e
      log_exception(e)
      Sentry.set_extras(profile_id: profile.id)
      Sentry.capture_exception(e)
    end
  end

  def need_to_archive(profile_data, profile)
    vacations_1c = profile_data['vacations'].map do |vacation_data|
      VacationPeriod.order(id: :asc).main_actual.find_by(profile: profile, start_date: vacation_data['start_date'], source_type: :exchange_1c)
    end
    archive_vacation_if_needed(profile, vacations_1c)
  end

  def archive_vacation_if_needed(profile, vacations)
    last_december = Date.today.prev_year.end_of_year.beginning_of_month
    statuses = VacationPeriod::CHECK_ARCHIVED_STATUSES

    profile.vacation_periods.where('start_date >= ?', last_december)
           .not_moved_vacation(statuses).each do |vacation|
      next if vacations.include?(vacation)

      vacation.archived!
      store_vacation_history(vacation, :vacation_not_in_1c, nil)
    end
  rescue StandardError => e
    log_exception(e)
    Sentry.set_extras(profile_id: profile.id)
    Sentry.capture_exception(e)
  end

  def main_vacation?(type, vacation, _schedule_type)
    return true unless vacation
    return unless VACATION_MAIN_TYPES.include?(type)

    # return false if vacation.archived? && !vacation.children_count.zero?
    # return false if !%w[cancelled scheduled].include?(vacation.status)
    # return false if vacation.parent_id.blank?
    vacation.children_count.zero? && vacation.parent_id.blank? && vacation.allowed_for_1c_update?
  end

  def planned_vacation?(type, vacation)
    return unless vacation
    return unless VACATION_MAIN_TYPES.include?(type)

    vacation.status == 'scheduled' && vacation.parent_id.blank?
  end

  def reschedule_vacation?(type, vacation)
    return unless vacation
    return unless VACATION_MAIN_TYPES.include?(type)

    vacation.parent_id.present?
  end

  def vacation_type(vacation_data)
    vtype = if enterprise_id.to_i == Employment::ENTERPRISE_RTK_IT_PLUS
              vacation_data['vacation_type'] = VacationPeriod.reverse_lookup_vacation_type(vacation_data['vacation_type'])
            else
              vacation_data['vacation_type']
            end

    raise "Неизвестный тип отпуска #{vacation_data['vacation_type']} для трудоустройства: #{enterprise_id}" if vtype.blank?

    vtype
  end

  def integration_1c_service_call(vacation_data, vacation, profile)
    vt = vacation_type(vacation_data)

    if main_vacation?(vt, vacation, vacation_data['vacation_schedule_type'])
      Integration1c::MainVacationService.new(vacation_data: vacation_data, vacation: vacation, profile: profile).call
    elsif planned_vacation?(vt, vacation)
      Integration1c::PlannedVacationService.new(vacation_data: vacation_data, vacation: vacation, profile: profile).call
    elsif reschedule_vacation?(vt, vacation)
      Integration1c::RescheduleVacationService.new(vacation_data: vacation_data, vacation: vacation, profile: profile).call
    else
      raise ProcessingError, "неизвестный процесс отпусков #{vacation_data.inspect}, #{vacation.inspect}, #{profile.inspect}"
    end
  end
end

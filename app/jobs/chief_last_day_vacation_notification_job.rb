# frozen_string_literal: true

class ChiefLastDayVacationNotificationJob < ApplicationJob
  include TimezoneCommon

  before_perform do |_job|
    @suitable_zones = find_suitable_timezones(VACATION_AUTO_PROCESS_DAY_HOUR)
  end

  def perform(*_args)
    notify_chiefs_who_did_not_approve_last_day
    notify_chiefs_who_did_not_approve_reschedule_last_day
    notify_chiefs_who_did_not_approve_unscheduled_last_day
  end

  def notify_functional_chief(vacation_period, assigned_chief)
    emailer = EmailerService::VacationsMailer.new(vacation_period, assigned_chief.chief)
    emailer.functional_chief_last_day_vacation_notification
  end

  def notify_line_chief(vacation_period, assigned_chief)
    emailer = EmailerService::VacationsMailer.new(vacation_period, assigned_chief.chief)
    emailer.line_chief_last_day_vacation_notification
  end

  def notify_chiefs_who_did_not_approve_last_day
    vacation_periods = VacationPeriod.left_joins([profile: [office: :city]]).merge(Profile.actual)
                                     .where(cities_clause_from_timezone(@suitable_zones), @suitable_zones)
                                     .where('status = ?', VacationPeriod.statuses[:on_approval])
                                     .where(source_type: :exchange_1c)

    vacation_periods.each do |vacation_period|
      next if vacation_period.last_approval_date != date_in_time_zone(@suitable_zones)

      vacation_period.assigned_vacation_chiefs.update_all(mailer_chief: true)

      f_chiefs = vacation_period.assigned_vacation_chiefs.functional_chiefs.select(&:inaction?)
      l_chiefs = vacation_period.assigned_vacation_chiefs.line_chiefs.mailer_chiefs.select(&:inaction?)

      f_chiefs.each do |assigned_chief|
        notify_functional_chief(vacation_period, assigned_chief)
      end

      l_chiefs.each do |assigned_chief|
        notify_line_chief(vacation_period, assigned_chief)
      end
    end
  end

  def notify_chiefs_who_did_not_approve_reschedule_last_day
    vacations = VacationPeriod.on_approval_with_parent.left_joins(profile: { office: :city }).merge(Profile.actual)
                              .where(cities_clause_from_timezone(@suitable_zones), @suitable_zones)

    vacations.each do |vacation|
      next if vacation.last_approval_date != date_in_time_zone(@suitable_zones)

      rescheduled_vacations = VacationPeriod.where(parent_id: vacation.parent_id)
      rescheduled_vacations.each { |reschedule| reschedule.assigned_vacation_chiefs.update_all(mailer_chief: true) }

      EmailerService::VacationsRescheduledMailer.notify_chiefs_about_last_day_for_reschedule_approval(rescheduled_vacations)
    end
  end

  def notify_chiefs_who_did_not_approve_unscheduled_last_day
    vacation_collections = VacationPeriod.left_joins([profile: [office: :city]]).merge(Profile.actual)
                                         .where(cities_clause_from_timezone(@suitable_zones), @suitable_zones)
                                         .where('status = ?', VacationPeriod.statuses[:on_approval])
                                         .where(source_type: :portal_creation)
                                         .order('start_date ASC')
                                         .group_by(&:vacation_collection_id).values

    vacation_collections.each do |vacations|
      next if vacations.none? { |vacation| vacation.last_approval_date == date_in_time_zone(@suitable_zones) }

      vacations.each { |unscheduled| unscheduled.assigned_vacation_chiefs.update_all(mailer_chief: true) }

      first_vacation = vacations.first
      f_chiefs = first_vacation.assigned_vacation_chiefs.functional_chiefs.select(&:inaction_on_creation?)
      l_chiefs = first_vacation.assigned_vacation_chiefs.line_chiefs.mailer_chiefs.select(&:inaction_on_creation?)
      as_chiefs = [f_chiefs, l_chiefs].flatten

      as_chiefs.each do |as_chief|
        emailer = EmailerService::VacationsUnscheduledMailer.new(vacations, as_chief.chief)
        if as_chief.is_line_chief && as_chief.chief != first_vacation.profile.parent
          # уведомление для вышестоящего руководителя | или заместителя
          # "Хочу в отпуск [письма]" Email вышестоящему руководителю в последний день для согласования отпуска вне графика
          emailer.notify_escalated_chief
        else
          emailer.notify_chiefs_who_did_not_approve_still_last_day
        end
      end
    end
  end
end

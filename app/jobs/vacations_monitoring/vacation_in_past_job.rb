# frozen_string_literal: true

class VacationsMonitoring::VacationInPastJob < ApplicationJob
  def perform
    vacations = VacationPeriod.joins(:profile).merge(Profile.actual)
                              .where('end_date < ?', Time.now.in_time_zone('Moscow').to_date)
                              .where.not(status: %w[cancelled archived used rejected]).distinct

    errors_array = []
    vacations.each do |vacation|
      errors_array << vacation.to_report_array
    end

    column_names = ['Айди отпуска', 'Дата начала отпуска', 'Дата окончания отпуска',
                    'Табельный профиля', 'ФИО профиля', 'Название департамерта']

    FormAndSendMonitoringReport.call(errors_array, 'Отпуска находятся в невалидном статусе после даты окончания', column_names)
  end
end

# frozen_string_literal: true

class VacationsMonitoring::Vacations1cCheckJob < ApplicationJob
  def perform
    @errors = []
    collect_data('/v2/1c/vacations/51102')
    collect_data('/v2/1c/vacations/50222')

    column_names = ['ФИО', 'табельный номер', 'дата увольнения', 'дата начала отпуска', 'дата окончания отпуска', 'статус отпуска', 'текст ошибки']
    file_name = 'Проверка_отпусков.xlsx'
    Axlsx::Package.new do |page|
      wb = page.workbook
      wb.add_worksheet(name: 'Отчет') do |sheet|
        sheet.add_row column_names
        @errors.each do |e|
          sheet.add_row e
          Rails.logger.debug(sheet)
        end
      end
      page.serialize("#{Rails.root}/#{file_name}")
    end

    VacationsMonitoringMailer.with(file_path: "#{Rails.root}/#{file_name}", subject: 'После синхронизации с 1с произошли расхождения с порталом',
                                   file_name: file_name).errors.deliver_now
  end

  def collect_data(endpoint)
    file = APIRequest.where(endpoint: endpoint, request_method: 'POST', response_code: 201).last.file.download
    data = JSON.parse(file.force_encoding('utf-8').delete(' ')[5..-1])

    data.each do |employee|
      profile = Profile.find_by(employee_number: employee['employee_number'].to_s)
      next unless profile.present?

      profile_vacations = profile.vacation_periods.where('start_date >= ? AND start_date <= ?',
                                                         Date.new(Date.today.year - 1, 12, 1), Date.new(Date.today.year + 1, 12, 31))
                                 .main_actual.where.not(status: 'rejected')

      check_count(employee['vacations'], profile, profile_vacations)

      check_payment_and_portal_presence(employee['vacations'], profile, profile_vacations)

      check_presence_in_1c(employee['vacations'], profile, profile_vacations)
    end
    @errors
  end

  def check_count(vacations_1c, profile, profile_vacations)
    if vacations_1c.size != profile_vacations.size
      @errors << [profile.full_name, profile.employee_number, profile.fire_date, nil, nil,
                  'Количество отпусков на портале отличается от количества в файле',
                  "Количество: в файле #{vacations_1c.size}, на портале #{profile_vacations.size}"]
    end
    @errors
  end

  def check_payment_and_portal_presence(vacations_1c, profile, profile_vacations)
    vacations_1c.each do |vacation|
      portal_vacation = profile_vacations.find_by(start_date: vacation['start_date'].to_date, end_date: vacation['end_date'].to_date)

      if portal_vacation.nil?
        @errors << [profile.full_name, profile.employee_number, profile.fire_date, vacation['start_date'],
                    vacation['end_date'], nil,
                    "Диапазон: отпуск с датами #{vacation['start_date']}-#{vacation['end_date']} отсутствует на портале"]
      elsif portal_vacation.payment_status != vacation['payment_status']
        @errors << [profile.full_name, profile.employee_number, profile.fire_date,
                    portal_vacation.start_date.strftime('%d/%m/%Y'),
                    portal_vacation.end_date.strftime('%d/%m/%Y'),
                    I18n.t("activerecord.attributes.vacation_period.statuses.#{portal_vacation.status}"),
                    "СтатусОплаты: отпуск с датами #{vacation['start_date']}/#{vacation['end_date']} в неверном статусе оплаты"]
      end
    end
    @errors
  end

  def check_presence_in_1c(vacations_1c, profile, profile_vacations)
    start_dates = vacations_1c.map { |vacation| vacation['start_date'].to_date }

    profile_vacations.each do |vacation|
      next if start_dates.include?(vacation.start_date)

      @errors << [profile.full_name, profile.employee_number, profile.fire_date,
                  vacation.start_date.strftime('%d/%m/%Y'),
                  vacation.end_date.strftime('%d/%m/%Y'),
                  I18n.t("activerecord.attributes.vacation_period.statuses.#{vacation.status}"),
                  "Нет в 1с: отпуск с датами #{vacation['start_date']}/#{vacation['end_date']} отсутствует в 1с"]
    end
    @errors
  end
end

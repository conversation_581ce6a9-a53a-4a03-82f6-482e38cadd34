# frozen_string_literal: true

class VacationsMonitoring::NoSuccessedResponsesJob < ApplicationJob
  def perform
    post_with_500_count = APIRequest.where('created_at > ?', Date.today.beginning_of_day)
                                    .where(request_method: 'POST')
                                    .where('response_code LIKE ?', '50%').count

    post_with_400_count = APIRequest.where('created_at > ?', Date.today.beginning_of_day)
                                    .where(request_method: 'POST')
                                    .where('response_code LIKE ?', '40%').count

    get_with_500_count = APIRequest.where('created_at > ?', Date.today.beginning_of_day)
                                   .where(request_method: 'GET')
                                   .where('response_code LIKE ?', '50%').count

    get_with_400_count = APIRequest.where('created_at > ?', Date.today.beginning_of_day)
                                   .where(request_method: 'GET')
                                   .where('response_code LIKE ?', '40%').count

    no_success_responses = APIRequest.where('created_at > ?', Date.today.beginning_of_day)
                                     .where('response_code LIKE ?', '20%').empty?

    VacationsMonitoringMailer.with(post_with_500_count: post_with_500_count,
                                   post_with_400_count: post_with_400_count,
                                   get_with_500_count: get_with_500_count,
                                   get_with_400_count: get_with_400_count,
                                   no_success_responses: no_success_responses).no_success_responses.deliver_now
  end
end

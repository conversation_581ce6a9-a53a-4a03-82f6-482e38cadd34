# frozen_string_literal: true

class AnnualVacationPlanningInEmployeesInArreasJob < ApplicationJob
  def perform(*_args)
    require 'csv'

    file_content = CSV.generate do |csv|
      Profile.all.each do |profile|
        @profile = profile

        next unless !profile.is_new_for_vacation_planning_reasons? && !profile.fired?

        next if profile.completed_planning_vacations?

        v = profile&.vacation_periods&.next_year&.order(:start_date)&.first

        csv << [profile.id, profile.email, profile.full_name, profile.parent.full_name, profile.department.title, v&.start_date,
                v&.status, next_year_vacations_days, should_be_planned_days]
      end
    end

    date = Date.current.to_date.iso8601
    File.write("#{date} - сотрудники которые частично согласовали или не согласовали графики отпусков.csv", file_content)
  end

  def next_year_vacations_days
    @profile.visible_vacation_periods
            .where('start_date >= ? and source_type = ?',
                   @profile.current_date_in_tz.beginning_of_year.next_year,
                   VacationPeriod.source_types[:exchange_1c])
            .sum(:days)
  rescue StandardError
    nil
  end

  def should_be_planned_days
    @profile.vacation_plannings.actual.sum(:days)
  rescue StandardError
    nil
  end
end

# frozen_string_literal: true

class UpcomingEventsNotifyJob < ApplicationJob
  include TelegramBot::BotHelpers
  include TimezoneCommon

  def perform(*_args)
    suitable_zones = find_suitable_timezones(ENV['DAY_HOUR_OF_EVENT_NOTIFY'])
    where_clause = suitable_zones.include?('Moscow') ? loose_time_zoned_clause : strict_time_zoned_clause

    not_notified_registrations = Registration
                                 .joins(:event)
                                 .where('EXTRACT(YEAR FROM events.scheduled_at) = ?', Date.today.year)
                                 .includes([profile: [office: :city]], :event)
                                 .where(where_clause, Date.today.day, Date.today.month, suitable_zones)

    not_notified_registrations.find_each do |registration|
      today_event_registration_reminder(registration)
    end

    event_day = DAYS_TILL_EVENT_NOTIFY.days.from_now

    not_notified_registrations = Registration
                                 .joins(:event)
                                 .where('EXTRACT(YEAR FROM events.scheduled_at) = ?', Date.today.year)
                                 .includes([profile: [office: :city]], :event)
                                 .where(where_clause, event_day.day, event_day.month, suitable_zones)

    not_notified_registrations.find_each do |registration|
      upcoming_event_registration_reminder(registration)
    end
  end

  private

  def strict_time_zoned_clause
    <<-SQL
      events.id = registrations.event_id
        and
      events.published_at is not null
        and
      extract(day from events.scheduled_at) = ?
        and
      extract(month from events.scheduled_at) = ?
        and
      (profiles.fire_date IS NULL OR profiles.fire_date >= '#{Time.now.in_time_zone('Moscow').to_date}')
        and
      #{default_cities_clause}
    SQL
  end

  def loose_time_zoned_clause
    <<-SQL
      events.id = registrations.event_id
        and
      events.published_at is not null
        and
      extract(day from events.scheduled_at) = ?
        and
      extract(month from events.scheduled_at) = ?
        and
      (profiles.fire_date IS NULL OR profiles.fire_date >= '#{Time.now.in_time_zone('Moscow').to_date}')
        and
      #{loose_cities_clause}
    SQL
  end

  def today_event_registration_reminder(registration)
    time_zone = registration.profile.office&.city&.time_zone

    UpcomingEventMailer
      .with(event: registration.event, user: registration.profile, destination_time_zone: time_zone, is_today: true)
      .notify
      .deliver_later

    upcoming_event = registration.event
    msg_title = I18n.t 'bot.today_event_remind',
                       deep_interpolation: true,
                       date: I18n.l(upcoming_event.scheduled_at, format: '%-d %B'),
                       theme: upcoming_event.title
    TelegramBot::Notifier.send_notification(
      registration.profile,
      event_telegram_msg(msg_title, upcoming_event, time_zone)
    )
  end

  def upcoming_event_registration_reminder(registration)
    time_zone = registration.profile.office&.city&.time_zone

    UpcomingEventMailer
      .with(event: registration.event, user: registration.profile, destination_time_zone: time_zone, is_today: false)
      .notify
      .deliver_later

    upcoming_event = registration.event
    msg_title = I18n.t 'bot.event_remind',
                       deep_interpolation: true,
                       days: DAYS_TILL_EVENT_NOTIFY.to_s,
                       theme: upcoming_event.title
    TelegramBot::Notifier.send_notification(
      registration.profile,
      event_telegram_msg(msg_title, upcoming_event, time_zone)
    )
  end
end

# frozen_string_literal: true

module Error::VacationPeriodsUpdateServicesErrorsHandler
  LOG_FILE = 'vp_update_service_job_error.log'

  def klean_logs
    File.write(LOG_FILE, '')
  end

  def handle_errors(err, item)
    Sentry.set_extras(item: item)
    Sentry.capture_exception(err)
    puts "Failed > #{err.class}"
    puts "Failed > #{err.message}"
    puts "Failed > #{err.backtrace.join("\n\r")}"
    puts "#{item}, #{err.message}"

    File.open(LOG_FILE, 'a+') do |file|
      file << "#{err.message}:  \n\r"
      file << "#{item}  \n\r"
      file << err.backtrace.join("\n\r")
    end
  end

  def item_employee_number_key_present?(item)
    raise 'Integrations 1C Vacation Periods Update Service Job: Employee number key empty' unless item.key?('employee_number')
  end

  def item_employee_number_present?(item)
    raise 'Integrations 1C Vacation Periods Update Service Job: Employee number empty' if item['employee_number'].blank?
  end

  def item_vacations_present?(vacations, employee_number)
    return unless vacations.blank?

    # VacationsMonitoringMailer.with(employee_number: employee_number).employee_vacations_empty.deliver_later

    raise "Integrations 1C Vacation Periods Update Service Job: Employee #{employee_number} vacations empty"
  end

  def employee_profile_present?(profile)
    raise 'Integrations 1C Vacation Periods Update Service Job: Employee profile not found' if profile.blank?
  end

  def vacations_duplicates_present?(employee_number, vacations)
    return if vacations.blank?

    grouped = vacations.group_by { |row| [row[:start_date], row[:end_date]] }
    @duplicates[employee_number] = grouped.values.select { |a| a.size > 1 }.flatten
    raise 'Integrations 1C Vacation Periods Update Service Job: Emloyer have duplicates vacations' unless @duplicates[employee_number].blank?
  end

  def all_vacations_archived?(profile)
    vacations = profile.vacation_periods.in_year(Date.current.year)
    archived_vacations = vacations.where(status: VacationPeriod.statuses[:archived])
    raise 'Integrations 1C Vacation Periods Update Service Job: Emloyer all vacations archieved' if vacations.count == archived_vacations.count
  end
end

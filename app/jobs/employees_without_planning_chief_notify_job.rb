# frozen_string_literal: true

class EmployeesWithoutPlanningChiefNotifyJob < ApplicationJob
  def perform(*_args)
    chiefs = Profile.actual.select(&:chief?)

    chiefs.each_with_index do |chief, idx|
      line_chief = Profile::LineChief.find(chief.id)
      next if line_chief.completed_chief_planning_campaign.count.zero?

      if line_chief.replacement_schedule.present?
        VacationsPlanningMailer.with(chief: line_chief.replacement_schedule.undertaken_profile, employees: line_chief.should_schedule_vacation)
                               .notify_chief_about_employees_without_planned_vacations
                               .deliver_later(wait: (idx * DELAY_BETWEEN_EMAILS_SECONDS).seconds)
      else
        VacationsPlanningMailer.with(chief: line_chief, employees: line_chief.should_schedule_vacation)
                               .notify_chief_about_employees_without_planned_vacations
                               .deliver_later(wait: (idx * DELAY_BETWEEN_EMAILS_SECONDS).seconds)
      end
    end
  end
end

# frozen_string_literal: true

class PinnedValidator < ActiveModel::Validator
  include Rails.application.routes.url_helpers

  def validate(record)
    return unless record.is_pinned?

    are_pinned = Post.where(is_pinned: true).where.not(id: record.id)
    return if are_pinned.count.zero?

    found_pinned_post = are_pinned.first

    record.errors[:is_pinned] << I18n.t('activerecord.errors.post.pinned_multiple', pinned_url: edit_admin_post_url(found_pinned_post.id))
  end
end

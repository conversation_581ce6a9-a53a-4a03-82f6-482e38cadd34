# frozen_string_literal: true

class FeedbackAPI < AuthenticatedAPI
  desc 'feedback_form_post'
  params do
    requires :subject, type: String, values: FEEDBACK.keys
    requires :message, type: String, length_text: LENGTH_MESSAGE
    optional :attachment, type: File, attachment_size: ATTACHMENT_MAX_SIZE
  end
  post '/' do
    attachment = params[:attachment]
    if attachment.present?
      filename = attachment['filename'].force_encoding('UTF-8')
      blob = ActiveStorage::Blob.create_after_upload!(io: attachment['tempfile'], filename: filename)
    end

    FeedbackMailer.feedback_form_message(current_user, blob, filename, params[:subject], params[:message]).deliver_later!

    status 201
  end

  desc 'Get subjects for feedback_form'
  get '/subjects' do
    present subjects: FEEDBACK.keys
  end
end

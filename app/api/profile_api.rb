# frozen_string_literal: true

class ProfileAPI < AuthenticatedAPI
  helpers do
    include AuthCodeHelpers

    def profile
      @profile ||= current_user.profile
    end

    def phone_valid?(phone)
      phone =~ /\A\d{11}\z/
    end

    def phone_busy?(phone)
      other_profile = Profile.actual.find_by(phone: phone)
      other_profile && other_profile[:id] != profile[:id]
    end

    def phone_verified?(phone)
      profile[:phone_verification_date] && profile[:phone] == phone
    end

    def pii_agreed?
      profile.personal_data_agreements.any?(&:pii?)
    end

    def exempt_from_agreement?
      profile.exempt_from_agreement?
    end
  end

  desc 'Current user profile'
  get '/' do
    entity_options = { with: Entities::Profile, type: :current }
    entity_options[:visitor_role] = :tag_admin if current_profile.can_add_tags_to_profile?
    present(profile, entity_options)
  end

  desc 'Agreed to PII'
  params do
    requires :pii, type: Boolean
    requires :code, type: String
  end
  post '/pii' do
    store = AuthCode::Store.new(id: current_profile.id)
    handle_code_errors!(store)
    wrong_code_attempt!(store) unless params[:code] == store[:code]

    profile.personal_data_agreements.create!(
      pii: params[:pii],
      fullname: profile.full_name,
      login: current_user.email,
      agreed_at: DateTime.now,
      session_hash: session.id,
      sms_code: params[:code],
      phone_number: profile.phone,
      email: profile.email,
      confirm_option: AuthCode::Store.new(id: profile.id).confirm_option
    )

    status 200
  end

  desc 'Agreed to PII'
  get '/pii' do
    {
      agreed: exempt_from_agreement? || pii_agreed?
    }
  end

  desc 'Update current user profile'
  params do
    optional :personal_email
    optional :phone
    optional :about
    optional :finished_onboarding, type: String, values: Profile::DEFAULT_ONBOARDINGS
    optional :was_cafeteria_landing_viewed, type: Boolean
    optional :avatar, type: File
  end
  patch '/' do
    profile_params = declared(params, include_missing: false)
    avatar = profile_params[:avatar]
    finished_onboarding = profile_params[:finished_onboarding]

    unless avatar.nil?
      blob = ActiveStorage::Blob.create_after_upload!(
        io: avatar['tempfile'], filename: avatar['filename'],
        content_type: avatar['type']
      )

      profile.avatar.attach(blob)
    end

    profile.finished_onboarding << finished_onboarding unless finished_onboarding.nil?

    profile.update!(profile_params.except(:avatar, :finished_onboarding))

    present profile, with: Entities::Profile, type: :current
  end

  desc 'Settings for current user profile'
  params do
    optional :birthday_visible, type: Boolean
    optional :phone_visible, type: Boolean
  end
  patch '/settings' do
    profile_params = declared(params, include_missing: false)
    profile.update!(profile_params)

    status 200
  end

  desc 'Subscribe/unsubscribe on other profile birthday'
  params do
    requires :id, type: Integer
  end
  patch '/subscribe_birthday_notification' do
    subscribed_on = Profile.find(params[:id])

    error!('BIRTHDAY_HIDDEN', 403) unless subscribed_on.birthday_visible?

    reminder_hash = { subscribed_on_id: subscribed_on.id, subscriber_id: profile.id }
    reminder = BirthdayReminder.find_by(reminder_hash)

    if reminder.present?
      reminder.destroy!
    else
      BirthdayReminder.create!(reminder_hash)
    end

    status 200
  end

  desc 'Send phone verification code to employee email'
  params do
    requires :phone, type: String
  end
  post '/verify_phone' do
    phone = params[:phone]

    store = AuthCode::Store.new(id: current_profile.id)

    raise_too_frequent_attempts!(store)
    error!(I18n.t('activerecord.errors.profile.phone'), 406) unless phone_valid?(phone)
    error!(I18n.t('phone_already_verified'), 403) if phone_verified?(phone)
    error!(I18n.t('phone_busy'), 403) if phone_busy?(phone)

    code, verify_param = generate_and_send_code(phone: phone, email: current_user.email, action_name: :verify_phone)
    store.save_code(code, verify_param: verify_param)

    {
      is_send_to_email: ApplicationRecord.verification_code_send_by_email?,
      confirmation_phone: phone.last(4)
    }
  rescue Errors::SmsSendingError => e
    error!(e.message, 403)
  end

  desc 'Validate phone by generated code'
  params do
    requires :code, type: String
    requires :phone, type: String
  end
  patch '/confirm_phone' do
    store = AuthCode::Store.new(id: current_profile.id)

    handle_code_errors!(store)
    # NOTE: убрал проверку, тк подтверждение может быть отправлено на почту. Запрос Олеси и Риты
    # error!(I18n.t('wrong_phone_to_verify'), 403) if params[:phone] != store[:verify_param]

    code = params[:code]
    if store.correct_code?(code)
      profile.update!(
        phone_verification_date: Time.now,
        phone: params[:phone]
      )
      status 200
    else
      store.count_code_confirm!
      error!(I18n.t('wrong_code'), 400)
    end
  end

  desc 'Save user push notification subscription info'
  params do
    requires :endpoint, type: String
    requires :p256dh, type: String
    requires :auth, type: String
  end
  patch '/push_notification_info' do
    notification = current_profile.push_notification_info
    return status 200 if notification&.update(params)

    notification_params = params.merge(profile_id: current_profile.id)
    PushNotificationInfo.create!(notification_params)
    status 201
  end

  desc 'Recommended functional chiefs'
  get '/recommended_functional_chiefs' do
    profiles = Profile.actual.distinct.joins({ account: { role: :role_permissions } }, :assignable_tags_to_profiles)
                      .where.not(id: profile.id)
                      .where(assignable_tags_to_profiles: { tag_id: profile.assigned_tags.pluck(:tag_id) })
                      .where(account: { role: { role_permissions: { code: TagProfilePolicy.to_s } } })

    present profiles, with: Entities::ProfileBasicInfo
  end
end

# frozen_string_literal: true

class ConsultationsAPI < AuthenticatedAPI
  desc 'Get user meetings'
  params do
    optional :date, type: String, values: %w[past future], default: 'future'
    optional :period, type: Integer, default: 30
  end
  get '/user_meetings' do
    date_to_time_range = {
      future: Time.now..params[:period].days.since,
      past: params[:period].days.ago..Time.now
    }

    started = date_to_time_range[params[:date].to_sym].first
    ended = date_to_time_range[params[:date].to_sym].last

    consultant = Consultant.active.find_by(profile_id: current_profile.id)
    all_consultations = if consultant.present?
                          consultant.consultations
                        else
                          Consultation.where(profile_id: current_profile.id)
                        end

    meetings = ConsultationMeeting.where(consultation_id: all_consultations.ids).where(meeting_date: started..ended)
    present meetings, with: Entities::ConsultationMeeting, type: :preview
  end

  desc 'Create consultation'
  params do
    requires :questions, type: String, length_text: LENGTH_FIELDS_CONSULTATION, desc: "Max #{LENGTH_FIELDS_CONSULTATION} symbol"
    requires :situation, type: String, length_text: LENGTH_FIELDS_CONSULTATION, desc: "Max #{LENGTH_FIELDS_CONSULTATION} symbol"
    requires :help, type: String, length_text: LENGTH_FIELDS_CONSULTATION, desc: "Max #{LENGTH_FIELDS_CONSULTATION} symbol"
    requires :consultant_id, type: Integer
    optional :files, type: Array do
      optional :file, type: File, desc: 'Files'
    end
  end
  post '/create' do
    if current_user.profile.consultations.where('created_at >= ?', DateTime.now.beginning_of_day).count >= MAX_NUMBER_CONSULTATIONS_PER_DAY
      error!(I18n.t('grape.consultation.exceed_limit_of_consultations_per_day'), 401)
    end
    consultant = Consultant.find(params[:consultant_id])
    consultation = Consultation.new(profile_id: current_user.profile.id,
                                    consultant_id: consultant.id,
                                    questions: params[:questions],
                                    situation: params[:situation],
                                    help: params[:help])

    if params[:files].present?
      params[:files].each do |file|
        blob = ActiveStorage::Blob.create_after_upload!(
          io: file['tempfile'], filename: file['filename'],
          content_type: file['type']
        )

        consultation.files.attach(blob)
      end
    end

    consultation.save!

    emailer = EmailerService::CareerConsultationsMailer.new(consultation)
    emailer.notify_consultant_about_new_consultation

    status 201
  end

  desc 'Create consultation meeting'
  route_param :id, type: Integer do
    params do
      requires :place, type: String, length_text: LENGTH_FIELDS_CONSULTATION, desc: "Max #{LENGTH_FIELDS_CONSULTATION} symbol"
      requires :date, type: {
        value: DateTime,
        message: I18n.t('grape.errors.event.type_date_time')
      }, desc: "Format : #{DateTime.now.strftime('%FT%T%:z')}"
    end
    post '/create_meeting' do
      consultation = Consultation.find(params[:id])

      authorize consultation, :can_change_meeting?, policy_class: ConsultationPolicy

      previous_meeting = consultation&.consultation_meetings&.last

      previous_meeting.update!(status: 'past') if previous_meeting.present?

      consultation_meeting = ConsultationMeeting.new(consultation_id: consultation.id,
                                                     meeting_place: params[:place],
                                                     meeting_date: params[:date])

      consultation_meeting.save!

      emailer = EmailerService::CareerConsultationsMailer.new(consultation)
      emailer.notify_employee_about_new_meeting(consultation_meeting)

      status 201
    end
  end

  desc 'Get all employee/consultant consultations'
  params do
    optional :type, type: String, values: %w[in_work closed], default: 'in_work'
    optional :page, type: Integer, default: ITEM_LIST_DEFAULT_PAGE_NUM, description: 'Current page'
    optional :per_page, type: Integer, default: ITEM_LIST_DEFAULT_PER_PAGE, description: 'Items per page'
  end
  get '/' do
    consultant = Consultant.active.find_by(profile_id: current_profile.id)
    all_consultations = if consultant.present?
                          Consultation.where(consultant_id: consultant.id)
                        else
                          Consultation.where(profile_id: current_profile.id)
                        end

    consultations = all_consultations.where(status: params[:type])
                                     .page(params[:page])
                                     .limit(params[:per_page])

    present :consultations, consultations, with: Entities::Consultation, current_profile: current_user.profile
    present :is_consultant, consultant.present?
    present :viewed_landing, current_user.profile.viewed_career_consulting_landing
  end

  route_param :id, type: Integer do
    desc 'Consultation by :id'
    get '/' do
      consultation = Consultation.find(params[:id])

      authorize consultation, :allow_for_users?, policy_class: ConsultationPolicy

      present consultation, with: Entities::Consultation, current_profile: current_user.profile, type: :detail
    end
  end

  desc 'Patch consultation meeting note'
  params do
    requires :id, type: Integer
    requires :value, type: String, desc: '"CHECKED" or some text'
  end
  patch '/consultation_meeting_note' do
    meeting_note = ConsultationMeetingNote.find(params[:id])

    authorize meeting_note.consultation_meeting.consultation, :can_change_meeting?, policy_class: ConsultationPolicy

    meeting_note.update!(
      value: params[:value]
    )
    status 200
  end

  desc 'Update consultation meeting'
  params do
    requires :meeting_id, type: Integer
    requires :place, type: String, length_text: LENGTH_FIELDS_CONSULTATION, desc: "Max #{LENGTH_FIELDS_CONSULTATION} symbol"
    requires :date, type: {
      value: DateTime,
      message: I18n.t('grape.errors.event.type_date_time')
    }, desc: "Format : #{DateTime.now.strftime('%FT%T%:z')}"
  end
  patch '/update_meeting' do
    consultation_meeting = ConsultationMeeting.find(params[:meeting_id])

    authorize consultation_meeting.consultation, :can_change_meeting?, policy_class: ConsultationPolicy

    error!(I18n.t('grape.consultation.meeting_date_in_the_past'), 403) if consultation_meeting.meeting_date < DateTime.now

    consultation_meeting.update!(meeting_place: params[:place],
                                 meeting_date: params[:date])

    status 200
  end

  desc 'Cancel consultation meeting'
  params do
    requires :meeting_id, type: Integer
  end
  delete '/cancel_meeting' do
    consultation_meeting = ConsultationMeeting.find(params[:meeting_id])

    authorize consultation_meeting.consultation, :can_change_meeting?, policy_class: ConsultationPolicy

    error!(I18n.t('grape.consultation.meeting_date_in_the_past'), 403) if consultation_meeting.meeting_date < DateTime.now

    consultation_meeting.destroy!
  end

  desc 'Close consultation'
  route_param :id, type: Integer do
    patch '/close' do
      consultation = Consultation.find(params[:id])

      authorize consultation, :allow_for_consultant?, policy_class: ConsultationPolicy

      consultation.consultation_meetings.each do |meeting|
        meeting.update!(status: 'past') if meeting.upcoming?
      end

      consultation.update!(status: 'closed')
    end
  end

  namespace :consultation_meeting do
    desc 'Get consultation meeting review'
    get '/:id/review' do
      consultation_meeting = ConsultationMeeting.find(params[:id])

      authorize consultation_meeting, :allow_for_users?, policy_class: ConsultationPolicy

      present consultation_meeting.consultation_meeting_review, with: Entities::ConsultationMeetingReview
    end

    desc 'Create consultation meeting review'
    params do
      requires :consultation_meeting_id, type: Integer
      requires :answer, length_text: LENGTH_FIELDS_CONSULTATION, desc: "Max #{LENGTH_FIELDS_CONSULTATION} symbol"
      requires :steps, length_text: LENGTH_FIELDS_CONSULTATION, desc: "Max #{LENGTH_FIELDS_CONSULTATION} symbol"
      requires :next_help, length_text: LENGTH_FIELDS_CONSULTATION, desc: "Max #{LENGTH_FIELDS_CONSULTATION} symbol"
      requires :expectations, length_text: LENGTH_FIELDS_CONSULTATION
    end
    post '/review' do
      consultation_meeting = ConsultationMeeting.find(params[:consultation_meeting_id])
      authorize consultation_meeting, :allow_left_review?, policy_class: ConsultationMeetingPolicy

      consultation_meeting.create_consultation_meeting_review(
        answer: params[:answer],
        steps: params[:steps],
        next_help: params[:next_help],
        expectations: params[:expectations]
      )

      status 201
    end
  end

  desc 'View career consulting landing'
  patch '/view_landing' do
    current_user.profile.update!(viewed_career_consulting_landing: true)

    status 201
  end
end

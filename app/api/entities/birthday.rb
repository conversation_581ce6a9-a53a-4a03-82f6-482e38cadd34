# frozen_string_literal: true

module Entities
  class Birthday < Grape::Entity
    include Rails.application.routes.url_helpers

    expose :id, documentation: { type: Integer }
    expose :first_name
    expose :middle_name, if: { type: :full }
    expose :last_name
    expose :position, if: { type: :full }
    expose :department, if: { type: :full }
    expose :birth_date
    expose :address, if: { type: :full }
    expose :avatar
    expose :city, if: { type: :full }
    expose :remind_of_birthday, if: { type: :full }
    expose :employments, using: Entities::Employment, documentation: { is_array: true }

    def department
      {
        id: object.department.id,
        title: object.department.title
      }
    end

    def address
      object.office&.address
    end

    def city
      object.office&.city&.name
    end

    def avatar
      url_for(object.avatar) unless object.avatar.attachment.nil?
    end

    def remind_of_birthday
      options[:current_profile].remind_of_birthday(object.id)
    end
  end
end

# frozen_string_literal: true

module Entities
  class RescheduledCollection < Grape::Entity
    include Entities::Concerns::VacationPolicies

    expose :canceled_vacation_period
    expose :vacations
    expose :status
    expose :chiefs
    expose :whois
    expose :comment
    expose :children_count
    expose :profile, as: :employee, using: Entities::ProfileBasicInfo, if: { type: :chief_view }
    expose :can_resubmit, unless: { type: :chief_view }
    expose :must_be_approved_again_later, if: { type: :chief_view }

    def vacation_object
      canonical_vacation
    end

    def children_count
      object[:vacations].count.to_i - 1
    end

    def comment
      return false unless canonical_vacation.present?

      on_approval_for_employee = canonical_vacation.on_approval? && options[:type] == :planning
      if options[:type] == :chief_view || on_approval_for_employee
        canonical_vacation.vacation_confirmations.where(is_chief: false).last&.comment
      else
        canonical_vacation.vacation_confirmations.where(is_chief: true).last&.comment
      end
    end

    def canonical_vacation
      object[:vacations]&.first
    end

    def profile
      object[:canceled_vacation_period].profile
    end

    def whois
      object[:vacations]&.first&.whois(options[:current_profile])
    end

    def canceled_vacation_period
      Entities::Vacations.represent(object[:canceled_vacation_period], type: :planning, current_profile: options[:current_profile])
    end

    def vacations
      Entities::Vacations.represent(object[:vacations], view: :reschedule_details, type: options[:type], current_profile: options[:current_profile],
                                                        whois: whois, is_line_chief: options[:current_profile].is_line_chief_for?(canonical_vacation))
    end

    def status
      object[:vacations]&.first&.status
    end

    def on_cancellation?
      object[:process] == :cancellation
    end

    def agreed_status
      on_cancellation? ? :agreed_on_cancellation : :agreed_on_rescheduling
    end

    def rejected_status
      on_cancellation? ? :rejected_on_cancellation : :rejected_on_rescheduling
    end

    def chiefs
      return unless object[:vacations].present?

      vacation = object[:vacations].first
      as_chiefs = vacation.assigned_vacation_chiefs

      as_chiefs.map do |as_chief|
        {
          id: as_chief.chief.id,
          last_name: as_chief.chief.last_name,
          first_name: as_chief.chief.first_name,
          middle_name: as_chief.chief.middle_name,
          is_line_chief: as_chief.is_line_chief,
          replacement: as_chief.replacement?,
          escalated_chief: as_chief.escalated?,
          status: vacation.vacation_confirmations
                          .where(status: [agreed_status, rejected_status])
                          .find_by(profile_id: as_chief.chief_id)&.detect_action
        }
      end.uniq
    end

    def must_be_approved_again_later
      object[:vacations].any? { |vacation| vacation.scheduled? || vacation.draft? }
    end
  end
end

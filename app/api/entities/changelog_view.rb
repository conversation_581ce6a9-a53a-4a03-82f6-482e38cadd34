# frozen_string_literal: true

module Entities
  class ChangelogView < Grape::Entity
    expose :id, documentation: { type: Integer }
    expose :title
    expose :body
    expose :status
    expose :published_at
    expose :updated_at
    expose :created_at
    expose :viewed_by_current_user
    expose :data_editor

    def viewed_by_current_user
      object.viewed_by?(options[:current_user].profile.id)
    end
  end
end

# frozen_string_literal: true

module Entities
  class DepartmentInfo < Grape::Entity
    expose :id, documentation: { type: Integer }
    expose :title
    expose :chief, documentation: { type: Hash }
    expose :path, documentation: { type: Hash, is_array: true }

    def chief
      nil
    end

    def path
      object.ancestors.map { |dep| { id: dep.id, title: dep.title } }
    end
  end

  class DepartmentChildren < Grape::Entity
    expose :id, documentation: { type: Integer }
    expose :title
    expose :tag, using: Entities::TagPreview
    expose :employee_count, documentation: { type: Integer }

    def tag
      object[:tag]
    end
  end

  class Department < Grape::Entity
    expose :department, merge: true, using: Entities::DepartmentInfo
    expose :subdepartments, using: Entities::DepartmentChildren, documentation: { is_array: true }
    expose :employees, using: Entities::ProfilePreview, documentation: { is_array: true }
  end
end

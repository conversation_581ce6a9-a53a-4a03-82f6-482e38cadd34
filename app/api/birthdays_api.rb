# frozen_string_literal: true

class BirthdaysAPI < AuthenticatedAPI
  helpers do
    def precedence_filter(date)
      query = Profile.includes(:office)
                     .where("extract(day from birth_date) = #{date.day}")
                     .where("extract(month from birth_date) = #{date.month}")
                     .where('birthday_visible = ?', true)
                     .actual
      profiles = if !current_user.profile.office&.city&.id.blank?
                   query.where(department_id: current_user.profile.department_id)
                        .or(query.where(office_id: current_user.profile.office_id))
                        .or(query.where(offices: { city_id: current_user.profile.office.city_id }))
                        .or(query)
                        .order(Arel.sql("department_id !=  #{current_user.profile.department_id.to_i}"))
                        .order(Arel.sql("office_id != #{current_user.profile.office_id.to_i}"))
                        .order(Arel.sql("offices.city_id != #{current_user.profile.office.city_id.to_i}"))
                        .order('last_name ASC', 'first_name ASC', 'middle_name ASC')
                 else
                   query.where(department_id: current_user.profile.department_id)
                        .or(query)
                        .order(Arel.sql("department_id !=  #{current_user.profile.department_id.to_i}"))
                        .order('last_name ASC', 'first_name ASC', 'middle_name ASC')
                 end

      profiles
    end
  end

  desc 'Widget list'
  get '/widget_list' do
    date = date_in_user_tz
    list = precedence_filter(date)
    today_only = true

    if list.count < WIDGET_SIZE
      current_date = date
      today_only = false
      while (date - current_date < 30) && (list.count < WIDGET_SIZE)
        date += 1
        list += precedence_filter(date)
      end
    end

    present :today_only, today_only
    present :birthdays_number_today, list.count if today_only
    present :plus_number, list.count - WIDGET_SIZE
    present :profiles, list.first(WIDGET_SIZE), with: Entities::Birthday
  end

  desc 'Birthdays list'
  params do
    optional :period, type: Integer, values: [7, 14, 30, 60], default: 14
    optional :filter_by, type: String, values: %w[city office all], default: 'city'
    optional :sort_by, type: String, default: 'date', values: %w[date name]
    optional :slider_level, type: Integer, default: 0, values: ->(v) { v >= 0 }
  end
  get '/' do
    query = Profile.includes(:office)
                   .joins(:department)
                   .where('birthday_visible = ?', true)
                   .actual

    date = date_in_user_tz
    leap_date = date.change(year: 2020)
    query = query.where("make_date(
        extract(year from to_date(:leap_date, 'YYYY-MM-DD'::Text) +
          (make_date(
              :leap_year,
              extract(month from birth_date)::int,
              extract(day from birth_date)::int
            )
            < to_date(:leap_date, 'YYYY-MM-DD'::Text))::int
          )::int,
        extract(month from birth_date)::int,
        extract(day from birth_date)::int
      ) BETWEEN to_date(:leap_date, 'YYYY-MM-DD'::Text) AND to_date(:leap_date, 'YYYY-MM-DD'::Text) + :period",
                        leap_date: leap_date.iso8601, leap_year: leap_date.year, period: params[:period])
    query = query.where.not('extract(month from birth_date) = 2 AND extract(day from birth_date) = 29') unless date.leap?

    slider = []
    department = Department.find(current_user.profile.department_id)
    query_department = department
    top_level = Department.root.level + 1
    department.level.downto top_level do
      count = query.where('departments.lft >= ? AND departments.rgt <= ?', query_department.lft, query_department.rgt).count
      slider[query_department.level - 1] = { department_id: query_department.id,
                                             employee_count: count }
      query_department = Department.find(query_department.parent_id) if query_department.level.positive?
    end

    query_department = if params[:slider_level] <= slider.size - 1
                         Department.find(slider[params[:slider_level]][:department_id])
                       else
                         Department.find(slider[slider.size - 1][:department_id])
                       end

    query = query.where('departments.lft >= ? AND departments.rgt <= ?', query_department.lft, query_department.rgt)

    query = if params[:filter_by] == 'office' && current_user.profile.office.blank?
              query.none
            elsif params[:filter_by] == 'office'
              query.where(office_id: current_user.profile.office_id)
            else
              query
            end
    query = if params[:filter_by] == 'city' && current_user.profile.office&.city&.id.blank?
              query.none
            elsif params[:filter_by] == 'city'
              query.where(offices: { city_id: current_user.profile.office.city_id })
            else
              query
            end

    query = query.order(Arel.sql('extract(month from birth_date) ASC'), Arel.sql('extract(day from birth_date) ASC')) if params[:sort_by] == 'date'
    query = query.order('last_name ASC', 'first_name ASC', 'middle_name ASC')

    present :config, [
      {
        period: params[:period],
        filter_by: params[:filter_by],
        sort_by: params[:sort_by],
        slider_level: query_department.level - 1,
        birthdays_count: query.count,
        slider_filter: slider
      }
    ]
    present :profiles, query, with: Entities::Birthday, current_profile: current_user.profile, type: :full
  end

  desc 'Subscriber list'
  get '/subscriber_list' do
    subscriber_list = BirthdayReminder.where(subscriber_id: current_user.profile.id)
    ids = subscriber_list.map(&:subscribed_on_id)

    profiles = Profile.actual.where('birthday_visible = ?', true).where(id: ids)

    present :birthdays, profiles, with: Entities::Birthday
  end
end

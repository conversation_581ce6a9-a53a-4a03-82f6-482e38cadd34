# frozen_string_literal: true

class HrPlannerAPI < AuthenticatedAPI
  class << self
    def request_params
      params do
        requires :profile_id, type: Integer
        requires :vacations, type: Array do
          requires :vacation_type, type: String
          requires :start_date, type: String
          requires :days, type: Integer
        end
      end
    end
  end

  helpers do
    include API::AuthCodeHelpers
    include API::VacationPlanningNotifyHelpers
    include API::VacationScheduleHelpers
    include API::VacationHistoryLogger
    include API::PlanningHelpers

    def record_vacation_planner(chief_id)
      Rails.cache.write(Profile::LineChief.submitted_1c_k, [*Rails.cache.read(Profile::LineChief.submitted_1c_k), chief_id])
    end

    def read_vacation_planner
      (Rails.cache.read(Profile::LineChief.submitted_1c_k) || []).uniq
    end
  end

  before do
    error!('Пользователь не является кадровиком и/или находится вне рамках планирования', 403) unless current_profile.hr_planner?
  end

  desc 'Получение должников по планированию'
  get '/' do
    chiefs_in_arrears_for_vacation_planning = Rails.cache.fetch(Profile::LineChief.in_arreas_k) do
      Profile::LineChief.all.reject { |line_chief| line_chief.completed_chief_planning_campaign.count.zero? }
    end
    present chiefs_in_arrears_for_vacation_planning, with: Entities::ProfileBasicInfo
  end

  desc 'Получение руководителей составивших планирование за последние 24 часа'
  get '/planned_vacation_chiefs' do
    present read_vacation_planner.as_json
  end

  request_params
  desc 'Создать график отпусков'
  post '/create' do
    create_vacations(:on_approval, found_profile)
    create_vacation_confirmations(:agreed_on_planning, current_profile, true, :notify_employee_that_chief_planned_vacations_for_him)
  end

  request_params
  desc 'Изменить график отпуска'
  put '/move_vacations' do
    archive_data!(sanitized_profile_id)
    @moved = true
    create_vacations(:on_approval, found_profile)
    create_vacation_confirmations(:agreed_on_planning, current_profile, true, :notify_employee_that_chief_planned_vacations_for_him)
  end

  desc 'запрос на доступ на отправку в 1С'
  params do
    requires :line_chief_profile_id
  end
  get '/vacation_ready_to_submit' do
    if VacationReadyToSubmitPolicy.new(current_user, nil, params[:line_chief_profile_id]).show?
      status 200
    else
      error!("Для пользователя #{params[:line_chief_profile_id]} запрещен доступ на отправку в 1С", 403)
    end
  end

  params do
    requires :line_chief_profile_id
  end
  desc 'Запрос на отправку кода подтверждения для кадровика на планиронии'
  post '/request_signature_code' do
    if VacationReadyToSubmitPolicy.new(current_user, nil, params[:line_chief_profile_id]).show?
      begin
        raise_too_frequent_attempts!
        code, verify_param = generate_code(phone: current_user.phone, email: current_user.email, action_name: :hr_planner_vacation_confirmation)
        default_code_store.save_code(code, verify_param: verify_param)
        {
          is_send_to_email: ApplicationRecord.verification_code_send_by_email?,
          confirmation_phone: current_user.phone&.send(:[], -4..)
        }
      rescue Errors::SmsSendingError => e
        error!(e.message, 403)
      end
    else
      error!("Для пользователя #{params[:line_chief_profile_id]} запрещен доступ на отправку в 1С", 403)
    end
  end

  desc 'Отправка в 1С графиков отпусков.'
  params do
    requires :line_chief_profile_id
    requires :code
    requires :comment
  end
  patch '/agreed' do
    if VacationReadyToSubmitPolicy.new(current_user, nil, params[:line_chief_profile_id]).show?
      handle_errors!
      handle_wrong_code! unless default_code_store.correct_code?(code)

      line_chief_profile = Profile.find(params[:line_chief_profile_id])
      @vacations = VacationPeriod.portal_planning.joins(:profile).where(profiles: { parent_id: line_chief_profile.id })
      @vacations.update_all(status: VacationPeriod.statuses[:scheduled])

      create_vacation_confirmations(:agreed_on_planning, current_profile, true, :notify_employee_that_planned_vacations_agreed)
      create_vacation_planner_reports(comment: params[:comment], is_hr_planned: true)
      record_vacation_planner(line_chief_profile.id)

      status 200
    else
      error!("Для пользователя #{params[:line_chief_profile_id]} запрещен доступ на отправку в 1С", 403)
    end
  end
end

# frozen_string_literal: true

class SecurityAPI < AuthenticatedAPI
  helpers do
    include AuthCodeHel<PERSON>

    def phone_verified?
      current_profile.phone && current_profile.phone_verification_date
    end
  end

  desc 'Generate auth code'
  post '/generate_auth_code' do
    error!(I18n.t('grape.errors.security.phone_not_verified'), 403) unless phone_verified?
    raise_too_frequent_attempts!

    code, _verify_param = generate_and_send_code(
      phone: current_user.phone,
      email: current_user.email,
      action_name: :vacation_confirmation
    )
    default_code_store.save_code(code)

    {
      is_send_to_email: ApplicationRecord.verification_code_send_by_email?,
      confirmation_phone: current_user.phone&.send(:[], -4..)
    }
  rescue Errors::SmsSendingError => e
    error!(e.message, 403)
  end

  desc 'Confirm information secure document'
  params do
    requires :code, type: String
  end
  patch '/confirm_infosecure_document' do
    store = default_code_store
    handle_code_errors!(store)

    if store.correct_code?(params[:code])
      current_profile.update!(
        infosecure_confirm_date: Date.today
      )
      status 200
    else
      store.count_code_confirm!
      error!(I18n.t('wrong_code'), 400)
    end
  end

  desc 'Confirm information secure document for plus'
  params do
    requires :code, type: String
  end
  patch '/confirm_infosecure_plus_document' do
    store = default_code_store
    handle_code_errors!(store)

    if store.correct_code?(params[:code])
      current_profile.update!(
        infosecure_plus_confirm_date: Date.today
      )
      status 200
    else
      store.count_code_confirm!
      error!(I18n.t('wrong_code'), 400)
    end
  end

  desc 'Code verification'
  params do
    requires :code, type: String
  end
  post '/verify_code' do
    store = default_code_store
    handle_code_errors!(store)

    return status 200 if store.correct_code?(params[:code])

    store.count_code_confirm!
    error!(I18n.t('wrong_code'), 400)
  end
end

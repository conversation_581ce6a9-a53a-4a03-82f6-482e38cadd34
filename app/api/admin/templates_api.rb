# frozen_string_literal: true
# frozen_string_literal: AdminAPI

class Admin::TemplatesAPI < AdminAPI
  desc 'Получить все шаблоны'
  get '/' do
    present Template.all, with: AdminEntities::Template
  end

  route_param :id do
    desc 'Получить шаблон по :id',
         success: {
           code: 200,
           model: AdminEntities::Template
         },
         failure: [
           {
             code: 404,
             message: 'Нет шаблона с таким :id',
             model: Entities::SimpleError
           },
           {
             code: 403,
             message: 'Нет прав для просмотра шаблона',
             model: Entities::SimpleError
           },
           {
             code: 422,
             message: ':id обязателен и должен быть целым числом',
             model: Entities::SimpleError
           }
         ]
    params do
      requires :id, type: Integer
    end

    get '/' do
      template = Template.find(params[:id])

      present template, with: AdminEntities::Template
    end
  end

  route_param :id do
    desc 'Редактировать шаблон по :id',
         success: {
           code: 200,
           model: AdminEntities::Template
         },
         failure: [
           {
             code: 404,
             message: 'Нет шаблона с таким :id',
             model: Entities::SimpleError
           },
           {
             code: 403,
             message: 'Нет прав для просмотра шаблона',
             model: Entities::SimpleError
           },
           {
             code: 422,
             message: ':id обязателен и должен быть целым числом',
             model: Entities::SimpleError
           }
         ]
    params do
      requires :id, type: Integer
      optional :title, type: String
      optional :template_type, type: String
      optional :value, type: Hash do
        requires :version, type: String
        requires :time, type: Integer
        requires :blocks, type: String
      end
    end

    patch '/' do
      template = Template.find(params[:id])

      template.update!(params)

      present template, with: AdminEntities::Template
    end
  end

  desc 'Создать шаблон',
       success: {
         code: 200,
         model: AdminEntities::Template
       },
       failure: [
         {
           code: 403,
           message: 'Нет прав для создания шаблона',
           model: Entities::SimpleError
         }
       ]
  params do
    requires :title, type: String
    requires :template_type, type: String
    requires :value, type: Hash do
      requires :version, type: String
      requires :time, type: Integer
      requires :blocks, type: String
    end
  end

  post '/' do
    template = Template.new

    template.update!(params)

    present template, with: AdminEntities::Template
  end

  route_param :id do
    desc 'Удалить шаблон по :id',
         success: {
           code: 200,
           model: AdminEntities::Template
         },
         failure: [
           {
             code: 404,
             message: 'Нет шаблона с таким :id',
             model: Entities::SimpleError
           },
           {
             code: 403,
             message: 'Нет прав для удаления шаблона',
             model: Entities::SimpleError
           },
           {
             code: 422,
             message: ':id обязателен и должен быть целым числом',
             model: Entities::SimpleError
           }
         ]
    params do
      requires :id, type: Integer
    end

    delete '/' do
      template = Template.find(params[:id])

      template.destroy

      present template, with: AdminEntities::Template
    end
  end
end

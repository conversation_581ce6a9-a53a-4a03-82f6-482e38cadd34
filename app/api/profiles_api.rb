# frozen_string_literal: true

class ProfilesAPI < AuthenticatedAPI
  desc 'Profiles list'
  params do
    optional :q, type: String, values: ->(v) { v.length >= PROFILE_SEARCH_QUERY_MAX_LENGTH }
    optional :page, type: Integer, default: PROFILE_SEARCH_DEFAULT_PAGE_NUM
    optional :per_page, type: Integer, default: PROFILE_SEARCH_DEFAULT_PER_PAGE
  end
  get '/' do
    query_string = params[:q]
    query_string ||= '*'

    profiles = Profile.unscoped
    profiles = profiles.where(enterprise_id: view_enterprise) if view_enterprise.present?
    profiles.search(query_string,
                    page: params[:page],
                    includes: :department,
                    per_page: params[:per_page],
                    highlight: true).with_highlights

    profiles_hashes = profiles.map do |profile|
      { profile: profile[0], highlights: profile[1] }
    end

    present(profiles_hashes, with: Entities::ProfilePreviewWithHighlights)
  end

  route_param :id, type: Integer do
    desc 'Profile by :id'
    get '/' do
      profile = Profile.find(params[:id])
      current_profile = current_user.profile

      entity_options = { with: Entities::Profile, remind_of_birthday: current_profile.remind_of_birthday(profile.id) }

      entity_options[:type] = :current if current_profile.id == params[:id]
      entity_options[:visitor_role] = :tag_admin if current_profile.can_add_tags_to_profile?

      present(profile, entity_options)
    end

    desc 'Get user\'s checklist'
    get '/checklist' do
      checklist = Profile.find(params[:id]).checklist

      present(checklist, with: Entities::Checklist, current_profile_id: current_profile.id)
    end

    desc 'Update user\'s checklist'
    params do
      requires :checklist_item, type: String, values: Checklist.permitted_params
    end
    patch '/checklist' do
      error!(I18n.t('grape.errors.checklist.not_owner_of_checklist'), 403) if current_profile.id != params[:id]

      checklist = Profile.find(params[:id]).checklist
      checklist.toggle!(params[:checklist_item])

      present(checklist, with: Entities::Checklist, current_profile_id: current_profile.id)
    end
  end

  desc 'Update tags in user profile'
  params do
    requires :id, type: Integer
    optional :tags, type: Array[Integer], coerce_with: ->(val) { val.delete('[').split(/\s+/).map(&:to_i) }, default: []
  end
  patch '/tags' do
    profile = Profile.find(params[:id])

    authorize nil, :add_tags_to_profile?, policy_class: TagEditPolicy

    all_tags = profile.assigned_tags.where(from_profile: true).pluck(:tag_id) | params[:tags]
    all_tags.each do |tag_id|
      if profile.assigned_tags.where(tag_id: tag_id, from_profile: true).exists?
        unless params[:tags].include?(tag_id)
          tag = Tag.find(tag_id)
          authorize tag, :show_tag?, policy_class: TagEditPolicy
          TagManager.delete_tag_from_profile(profile, tag)
        end
      else
        tag = Tag.find(tag_id)
        authorize tag, :show_tag?, policy_class: TagEditPolicy
        TagManager.add_tag_to_profile(profile, tag)
      end
    end

    status 201
  end
end

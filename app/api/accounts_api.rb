# frozen_string_literal: true

class AccountsAPI < AuthenticatedAPI
  desc 'Cafeteria role update'
  params do
    requires :email, type: String, desc: 'New role account email'
    requires :role, type: String, desc: 'Cafeteria role'
  end
  patch '/cafeteria_admin' do
    email = params[:email].downcase
    account = Account.find_by(email: email)
    error!(I18n.t('grape.errors.auth.no_account'), 403) unless account
    error!(I18n.t('grape.errors.account.not_cafeteria_admin'), 403) unless current_user.cafeteria_role == 'ROLE_ADMIN'

    account.update!(cafeteria_role: params[:role])
    status 200
  end
end

# frozen_string_literal: true

class VacationReport
  def initialize(email_to, start_date: nil, end_date: nil)
    @start_date = start_date.presence || Date.current.beginning_of_year
    @end_date = end_date.presence || Date.current.end_of_year
    @email_to = email_to
  end

  def to_csv
    require 'csv'

    file_content = CSV.generate(col_sep: ';') do |csv|
      csv.to_io.write "\uFEFF"
      csv << headers
      report_lines.compact.uniq.each { |line| csv << line }
    end

    file_content
  end

  def headers
    [
      I18n.t('extractables.vacation_report.id'),
      I18n.t('extractables.vacation_report.created_on_planning'),
      I18n.t('extractables.vacation_report.scheduled'),
      I18n.t('extractables.vacation_report.rejected'),
      I18n.t('extractables.vacation_report.moved'),
      I18n.t('extractables.vacation_report.employee_confirmed'),
      I18n.t('extractables.vacation_report.chief_confirmed'),
      I18n.t('extractables.vacation_report.vacation_schedule'),
      I18n.t('extractables.vacation_report.profile_id'),
      I18n.t('extractables.vacation_report.status'),
      I18n.t('extractables.vacation_report.start_date'),
      I18n.t('extractables.vacation_report.end_date'),
      I18n.t('extractables.vacation_report.created_at')
    ]
  end

  def report_lines
    VacationPeriod.main_actual
                  .joins(:vacation_histories)
                  .where('vacation_periods.start_date >= :start_date and vacation_periods.end_date <= :end_date',
                         start_date: @start_date, end_date: @end_date).map do |period|
      [
        period.id,
        created_on_planning?(period),
        was_scheduled?(period),
        was_rejected?(period),
        was_moved?(period),

        did_employee_confirmed?(period),
        did_chief_confirmed?(period),
        on_vacation_schedule?(period),

        period.profile_id,
        period.status,
        period.start_date.to_date.iso8601,
        period.end_date.to_date.iso8601,
        period.created_at.to_date.iso8601
      ]
    end
  end

  def created_on_planning?(period)
    return '1' if period.vacation_histories.created_on_planning.present?

    '0'
  end

  def was_scheduled?(period)
    return '1' if period.scheduled?
    return '1' if period.vacation_histories.confirmation_chief.present?

    '0'
  end

  def was_rejected?(period)
    return '1' if period.vacation_histories.reject_scheduled_vacation.present?
    return '1' if period.vacation_histories.reject_on_planning.present?
    return '1' if period.rejected?

    '0'
  end

  def was_moved?(period)
    return '1' if period.vacation_histories.deleted_on_planning.present?

    '0'
  end

  # NOTE: какое подтверждение требуется?
  def did_chief_confirmed?(period)
    return '1' if period.approved_by_all?
    return '1' if period.vacation_confirmations.agreed_on_planning.where(is_line_chief: true)

    '0'
  end

  def did_employee_confirmed?(period)
    return '1' if period.vacation_histories.confirmation_employee.present?
    return '1' if period.vacation_confirmations.agreed_on_planning.where(is_line_chief: false)
    return '1' if period.vacation_confirmations.agreed.where(is_line_chief: false)

    '0'
  end

  def on_vacation_schedule?(period)
    return '1' if period.schedule_collection.present?

    '0'
  end
end

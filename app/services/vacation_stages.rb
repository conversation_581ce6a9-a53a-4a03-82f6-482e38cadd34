# frozen_string_literal: true

class VacationStages
  def initialize(profile)
    @profile = profile
    @current_year = @profile.current_date_in_tz.year
  end

  # NOTE: это хак, к сожалению был внедрен разработчиком, так как год планирования это год НА которые планирует а не С которого
  #   например с 2021 на 2022
  def current_year_for_planning
    planning_year - 1
  end

  def planning_year(date = @profile.current_date_in_tz)
    if (first_stage.first..date.end_of_year).to_a.include?(date) || allowed_for_hr_planner?
      date.next_year.year
    elsif (date.beginning_of_year..confirmation_stage.last).to_a.include?(date)
      date.year
    else
      date.year
    end
  end

  def vacation_planning_start_date
    date = format('%<d_m>s.%<y>s', d_m: VACATION_PLANNING_START_DATE, y: current_year_for_planning)
    Date.parse(date)
  end

  def beginning_of_planning_year
    Date.parse("01.01.#{planning_year}")
  end

  def stage
    if first_stage?
      1
    elsif second_stage?
      2
    else
      0
    end
  end

  def planning_second_stage_and_after?
    @profile.current_date_in_tz.between?(second_stage.first, third_stage.second) ||
      @profile.current_date_in_tz.between?(allowed_for_hr_planner.first, allowed_for_hr_planner.second)
  end

  def first_stage
    start_date = parse_date(VACATION_PLANNING_START_DATE)
    end_date   = parse_date(VACATION_PLANNING_END_DATE)

    [start_date, end_date]
  end

  def second_stage
    start_date = parse_date(VACATION_CHIEF_CONFIRMATION_ALLOWED_START_DATE)
    end_date   = parse_date(VACATION_CHIEF_CONFIRMATION_ALLOWED_END_DATE)

    [start_date, end_date]
  end

  def third_stage
    start_date = parse_date(VACATION_1C_DOC_PREPARATION_START_DATE)
    end_date   = parse_date(VACATION_1C_DOC_PREPARATION_END_DATE)

    [start_date, end_date]
  end

  def allowed_for_hr_planner
    start_date = parse_date(db_data.allowed_for_hr_planner.first || VACATION_HR_PLANNER_START_DATE)
    end_date = parse_date(db_data.allowed_for_hr_planner.second || VACATION_HR_PLANNER_END_DATE)

    [start_date, end_date]
  end

  # TODO: переделать под 2-годичную систему счисления
  def agreement_stage
    [1.day.since(parse_date(VACATION_AGREEMENT_DATE)), parse_date(VACATION_EMPLOYEE_LOAD_UNCONFIRMED_DATE)]
  end

  def confirmation_stage
    [1.day.since(parse_date(VACATION_EMPLOYEE_LOAD_UNCONFIRMED_DATE)), parse_date(VACATION_EMPLOYEE_LOAD_CONFIRMED_DATE)]
  end

  %i[first_stage second_stage agreement_stage confirmation_stage allowed_for_hr_planner].each do |s_name|
    define_method("#{s_name}?".to_sym) do
      @profile.current_date_in_tz.between?(*self.send(s_name))
    end

    define_method("#{s_name}_date_range".to_sym) do
      (send(s_name)[0]..send(s_name)[1])
    end
  end

  private

  def parse_date(date_and_month, year = @current_year)
    Date.parse(format('%<d_m>s.%<year>s', d_m: date_and_month, year: year))
  end

  def db_data
    VacationStagesData.last
  end
end

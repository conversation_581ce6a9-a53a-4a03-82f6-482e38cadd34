# frozen_string_literal: true

module TelegramBot
  class EmployeeReportBuilder
    def self.build(employee)
      tel = employee.phone if employee.phone_visible
      tel = "+#{employee.phone}" if tel.present?
      office = employee.office
      info = "#{employee.full_name}\n"\
             "#{employee.position}\n"\
             "#{employee.department.title}\n"\
             "#{employee.email}\n"\
             "#{tel}\n"\
             "#{office&.city&.name}\n"\
             "#{office&.address}"
      info.gsub(/^\s+/, '')
    end
  end
end

# frozen_string_literal: true

module TelegramBot
  class TelegramAvatarUrl
    def self.fetch(telegram_id)
      photos_info = Telegram.bot.get_user_profile_photos(user_id: telegram_id, limit: 1)
      raise Errors::TelegramError, I18n.t('bot.errors.no_connection') unless photos_info.respond_to?('dig')

      user_photos = photos_info.dig('result', 'photos')
      return if user_photos.nil? || user_photos.empty?

      file_id = user_photos.flatten.first['file_id']
      file_info = Telegram.bot.get_file(file_id: file_id)
      file_path = file_info.dig('result', 'file_path')
      "#{TELEGRAM_API_LINK}/file/bot#{ENV['BOT_TOKEN']}/#{file_path}"
    end
  end
end

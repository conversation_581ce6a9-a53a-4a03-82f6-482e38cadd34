# frozen_string_literal: true

module Admin
  class PostsOnModerateNotifier
    def self.call
      accounts_with_post_publish_policy = Account.joins(role: :role_permissions)
                                                 .where(role: { role_permissions: { code: 'PostStatusChangePolicy' } })

      posts_on_moderation = Post.where(status: 'on_moderation')

      return if posts_on_moderation.empty?

      accounts_with_post_publish_policy.each do |account|
        posts_to_send = PostPolicy::Scope.new(account, posts_on_moderation)
                                         .resolve

        next if posts_to_send.empty?

        PostsMailer.with(account: account, posts: posts_to_send.to_a)
                   .posts_on_moderate_email.deliver_later
      end

      nil
    end
  end
end

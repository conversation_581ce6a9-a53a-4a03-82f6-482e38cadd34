# frozen_string_literal: true

require 'net/http'

module AuthCode
  class SmsSender
    def self.text_template(sms_template_id)
      {
        'external-id1': ENV['SMS_GATE_EXTERNAL_ID1'],
        'external-id3': ENV['SMS_GATE_EXTERNAL_ID3'],
        'messages': [{
          'template-id': sms_template_id,
          'message-id': Time.now.to_i.to_s
        }]
      }
    end

    def self.field_no(sms_template_id)
      if sms_template_id.to_i == SMS_TEMPLATE_ID[:verify_phone].to_i
        1
      elsif sms_template_id.to_i == SMS_TEMPLATE_ID[:vacation_confirmation].to_i
        2
      end
    end

    def self.request_body_builder(phone, code, action_name)
      req_body = text_template(SMS_TEMPLATE_ID[action_name])
      req_body[:messages][0].merge!(
        'recipient': phone,
        'variables': {
          "field#{field_no(SMS_TEMPLATE_ID[action_name])}": code
        }
      )
      req_body
    end

    def self.send(phone, code, action_name)
      if ENV['SMS_GATE_SEND_SMS_URL'].blank?
        Rails.logger.warn "missing SMS_GATE_SEND_SMS_URL: #{ENV['SMS_GATE_SEND_SMS_URL']}"
        return false
      end

      available_sms_in_current_month = Rails.cache.fetch(
        SMS_LIMIT_KEY,
        SMS_STORE_LIMIT_OPTIONS
      ) { MONTHLY_SMS_LIMIT_COUNT }
      return if available_sms_in_current_month.zero?

      uri = URI.parse(ENV['SMS_GATE_SEND_SMS_URL'])
      req = Net::HTTP::Post.new(uri, 'Content-Type' => 'application/json')
      req.basic_auth(ENV['EIP_LOGIN'], ENV['EIP_PWD'])
      request_body = request_body_builder(phone, code, action_name)
      req.body = request_body.to_json

      Net::HTTP.start(uri.hostname, uri.port) do |http|
        response = http.request(req)
        SmsMetrics.send_sms_request_metrics(status: response.code)
        raise Errors::SmsSendingError unless response.code.eql?('200')

        available_sms_in_current_month -= 1
        sms_limit_options = available_sms_in_current_month.zero? ? sms_reached_limit_options! : SMS_STORE_LIMIT_OPTIONS
        Rails.cache.write(SMS_LIMIT_KEY, available_sms_in_current_month, sms_limit_options)

        message_id = request_body[:messages][0][:'message-id']
        Rails.cache.write(
          "#{SMS_MESSAGE_ID_STORE_PREFIX}#{message_id}",
          { phone: phone, transmitted: Time.now },
          SMS_MESSAGE_INFO_STORE_OPTIONS
        )
      end
    end

    def self.sms_reached_limit_options!
      SmsLimitReachedMailer.notify_admin.deliver_later!
      Setting.find_by(name: CODE_BY_EMAIL_SETTING).update!(mode: true)
      SMS_STORE_LIMIT_OPTIONS.merge(
        expires_in: (Date.today.end_of_month - Date.today).to_i.days
      )
    end
  end
end

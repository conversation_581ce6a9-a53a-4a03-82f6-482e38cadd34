# frozen_string_literal: true

module AuthCode
  class Store
    CHIEF_ATTEMPTS_LIMIT = ENV.fetch('CHIEF_ATTEMPTS_LIMIT') { 20 }
    USER_ATTEMPTS_LIMIT = 3
    CODE_ID_PREFIX = 'auth_code'
    CODE_EXPIRATION_TIME = 3.minutes

    attr_reader :store, :store_id
    delegate :[], to: :@store

    # NOTE: schema
    # code: значение - код подтверждения
    #    должен сбрасываться при успешном подтверждении кода
    # code_expired: величина - время испарения кода
    #    должен сбрасываться при успешном подтверждении кода
    # code_confirmed: величина - кол-во НЕ КОРРЕКТНЫХ подтверждений кода
    #    должен сбрасываться при успешном подтверждении кода
    # verify_param: значение - куда было отправлено? номер телефона или адрес эл почты
    # was_confirmed: значение - булeво - было ли подтвержденео
    # confirmed_by: значение - как было подтверждено (смс/емейл)
    # next_code_verify_time: величина - когда в следующий раз можно запрашивать код
    #    30 секунд при обычных запрашиваниях
    #    или 10 минут при исчерпывании запросов)
    #    должен сбрасываться при успешном подтверждении кода
    # verifies: величина - кол-во запросов кода
    def initialize(id:)
      @id = id
      @store_id = "#{CODE_ID_PREFIX}_#{id}"
      @store ||= Rails.cache.fetch(store_id, AUTH_CODE_STORE_OPTIONS) { {} }
    end

    def save_code(code, verify_param = nil)
      count_code_verify_attempt
      store.merge!(
        code: code,
        code_expired: Time.now + CODE_EXPIRATION_TIME,
        code_confirmed: 0,
        verify_param: verify_param&.values&.first,
        was_confirmed: false,
        confirmed_by: confirm_option
      )
      flush_store!
    end

    def report_code_signature
      I18n.t('auth_code.send_to', code: store[:confirmed_code], delivery_to: store[:verify_param], delivery_method: store[:confirmed_by])
    end

    def can_verify_code?
      key_expired?(:next_code_verify_time)
    end

    def code_expired?
      key_expired?(:code_expired)
    end

    def many_code_confirms?
      store[:code_confirmed] >= USER_ATTEMPTS_LIMIT
    end

    def verify_limit_exceed?
      store[:verifies] >= attempts_limit
    end

    def current_profile
      @current_profile ||= Profile.find(@id)
    end

    def chief?
      current_profile.assigned_vacation_chiefs.count.positive?
    end

    def attempts_limit
      if chief?
        CHIEF_ATTEMPTS_LIMIT
      else
        USER_ATTEMPTS_LIMIT
      end
    end

    def verify_period_limit
      30.seconds
    end

    def count_code_confirm!
      store[:code_confirmed] += 1

      flush_store!
    end

    def generate_new
      Encryption::TimeBasedPasswordGenerator.run(current_profile.phone)
    end

    def correct_code?(code)
      store[:was_confirmed] = (code == store[:code])

      if store[:was_confirmed]
        store[:confirmed_code] = store[:code]
        store[:code] = generate_new
        store[:next_code_verify_time] = nil

        flush_store!
      end

      store[:was_confirmed]
    end

    def code_valid?(code)
      code == store[:code]
    end

    def confirm_code(code)
      return false unless code_valid?(code)

      store[:was_confirmed] = true
      store[:confirmed_code] = store[:code]
      store[:code] = nil

      flush_store!
    end

    def code_confirmed?
      store[:was_confirmed].equal?(true)
    end

    def reset_retries_counter
      store[:verifies] = 0
      store[:next_code_verify_time] = nil

      flush_store!
    end

    def next_code_after
      (store[:next_code_verify_time]&.to_time.to_i - Time.now.to_i).seconds.from_now.to_i
    end

    def clear_store!
      Rails.cache.delete(store_id, AUTH_CODE_STORE_OPTIONS)
    end

    def count_code_verify_attempt
      store[:verifies] ||= 0
      store[:verifies] += 1

      verify_period = verify_period_limit
      if verify_limit_exceed?
        Rails.logger.info '[authcode::store] exceeded limit'
        store[:verifies] = 0
        verify_period = 10.minutes
      end

      store[:next_code_verify_time] = Time.now + verify_period

      flush_store!
    end

    def key_expired?(key)
      return true unless store[key]

      store[key] < Time.now
    end

    def flush_store!
      Rails.cache.write(store_id, store, AUTH_CODE_STORE_OPTIONS)
    end

    def confirm_option
      ApplicationRecord.verification_code_send_by_email? ? :email : :sms
    end
  end
end

# frozen_string_literal: true

require 'rest-client'

module Setka
  class AssetFetcher
    include Setka::AssetsConst

    ASSETS_INFO_CACHE_KEY = 'SETKA_ASSETS_INFO'
    HASH_PATHS = {
      PUBLIC_JS_NAME => [:plugins, 0],
      PUBLIC_CSS_NAME => [:theme_files, 0],
      EDITOR_JS_NAME => [:content_editor_files, 1],
      EDITOR_CSS_NAME => [:content_editor_files, 0],
      JSON_CONFIG_NAME => [:theme_files, 1]
    }.freeze

    ASSETS_CACHE_KEYS = [
      PUBLIC_JS_NAME, PUBLIC_CSS_NAME, EDITOR_JS_NAME,
      EDITOR_CSS_NAME, JSON_CONFIG_NAME, ASSETS_INFO_CACHE_KEY
    ].freeze

    def call(asset_name)
      fetch_from_cache(ASSETS_INFO_CACHE_KEY, fetch_assets_info)
      fetch_from_cache(asset_name, fetch_asset(asset_name))
    end

    def editor_config
      fetch_from_cache(ASSETS_INFO_CACHE_KEY, fetch_assets_info)
      fetch_from_cache(JSON_CONFIG_NAME, fetch_editor)
    end

    def refresh
      ASSETS_CACHE_KEYS.each { |key| Rails.cache.delete(key) }
    end

    def update_assets
      http_threads = []
      http_threads << Thread.new { fetch_from_cache(ASSETS_INFO_CACHE_KEY, fetch_assets_info) }
      http_threads << Thread.new { fetch_from_cache(JSON_CONFIG_NAME, fetch_editor) }

      assets_keys = [PUBLIC_JS_NAME, PUBLIC_CSS_NAME, EDITOR_JS_NAME,
                     EDITOR_CSS_NAME]
      assets_keys.each do |asset_key|
        http_threads << Thread.new { fetch_from_cache(asset_key, fetch_asset(asset_key)) }
      end

      http_threads.each(&:join)
    end

    # private

    def fetch_from_cache(key, value)
      Rails.cache.fetch(key, expires_in: 365.days) { value }
    end

    def write_to_cache(key, value)
      Rails.cache.write(key, value, expires_in: 365.days)
    end

    def fetch
      uri = URI(endpoint('/builds/current'))
      params = { token: token }

      uri.query = URI.encode_www_form(params)

      RestClient.get(uri.to_s)
    end

    def fetch_assets_info
      JSON.parse(fetch.body)
    end

    def fetch_asset(asset_name)
      info = Rails.cache.read(ASSETS_INFO_CACHE_KEY)
      asset_hash_info = HASH_PATHS[asset_name]

      file_type = asset_hash_info[0]
      index = asset_hash_info[1]

      asset_url = info[file_type.to_s][index]['url']

      RestClient.get(asset_url).body
    end

    def fetch_editor
      info = Rails.cache.read(ASSETS_INFO_CACHE_KEY)
      config_url = info['theme_files'][1]['url']
      RestClient.get(config_url).body.force_encoding('UTF-8')
    end

    def endpoint(path)
      'https://editor.setka.io/api/v1/custom/' + path
    end

    def token
      ENV.fetch('SETKA_LICENSE_KEY')
    end
  end
end

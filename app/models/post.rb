# frozen_string_literal: true

class Post < ApplicationRecord
  belongs_to :profile, optional: true

  has_many_attached :images
  has_and_belongs_to_many :tags

  has_and_belongs_to_many :cities
  has_and_belongs_to_many :offices

  enum status: %i[draft published on_moderation scheduled]
  has_many :likes
  has_many :profiles, through: :likes

  has_many :views
  has_many :profiles, through: :views

  validates :title, presence: true, uniqueness: true
  validates_with FutureValidator
  validates_with PinnedValidator

  before_update :set_publised_at_after_publish
  # after_validation :set_correct_status!

  PREVIEW_DIVIDER_SELECTOR = ':contains("!####!"):not(:has(:contains("!####!")))'

  def status
    return 'scheduled' if scheduled?

    self[:status]
  end

  def preview
    parsed_body = Nokogiri::HTML.fragment(body)

    nodes_after_preview_divider = parsed_body.css("#{PREVIEW_DIVIDER_SELECTOR} ~ *")
    parsed_body.css(PREVIEW_DIVIDER_SELECTOR).first&.remove

    return nil if nodes_after_preview_divider.empty?

    nodes_after_preview_divider.each(&:remove)
    parsed_body.to_s
  end

  def body_without_divider
    parsed_body = Nokogiri::HTML.fragment(body)

    divider_element = parsed_body.css(PREVIEW_DIVIDER_SELECTOR).first

    return body if divider_element.nil?

    divider_element.remove
    parsed_body.to_s
  end

  scope :cities_children_of, lambda { |*parents_array|
    return includes(:cities).where(cities: { id: parents_array + [nil] }) if parents_array.include?('all')

    return includes(:cities).where(cities: { id: parents_array })
  }

  scope :tags_children_of, lambda { |*parents_array|
    return includes(:posts_tags).where(posts_tags: { tag_id: parents_array + [nil] }) if parents_array.include?('without')

    return includes(:posts_tags).where(posts_tags: { tag_id: parents_array })
  }

  scope :pinned, -> { where(is_pinned: true) }

  def self.ransackable_scopes(_auth_object = nil)
    %i[cities_children_of tags_children_of]
  end

  # Переопределение булевых значений "0", "1", "t"
  def self.ransackable_scopes_skip_sanitize_args
    %i[cities_children_of tags_children_of]
  end

  searchkick searchable: %i[title body],
             word_middle: %i[title body]

  def search_data
    {
      title: self.title,
      body: ActionController::Base.helpers.strip_tags(self.body),
      status: self.status,
      published_at: self.published_at
    }
  end

  delegate :count, to: :views, prefix: true

  def viewed_by?(user_id)
    views.where(profile_id: user_id).exists?
  end

  def like_count
    likes.count
  end

  def like_flag(user_id)
    likes.where(profile_id: user_id).exists?
  end

  def scheduled?
    self.published_at.present? && self.published_at > DateTime.now && self[:status] == 'published'
  end

  def self.with_tag(tag_id)
    includes(:posts_tags).where(posts_tags: { tag_id: tag_id })
  end

  private

  # def set_correct_status!
  #   return self.status = 'published' if status == 'scheduled'
  # end

  def set_publised_at_after_publish
    return self.published_at = DateTime.now if self.published_at.nil? && self.status == 'published'
  end
end

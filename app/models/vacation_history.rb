# frozen_string_literal: true

class VacationHistory < ApplicationRecord
  belongs_to :vacation_period

  BASE_EVENTS = {
    created_on_planning: 0,
    created_on_portal: 1,
    created_on_moved: 2,
    created_from_1c: 3,
    first_notify_created: 4,
    confirmation_employee: 5,
    add_functional_chiefs: 6,
    confirmation_chief: 7,
    reject_scheduled_vacation: 8,
    created_order_status: 9,
    dates_changed: 10,
    payment_transfer_status: 11,
    used_status: 12,
    vacation_not_in_1c: 13,
    agreed_deduction: 14,
    agreed_recalculation: 15,
    escalation: 16,
    chiefs_who_did_not_approve_vacation: 17,
    employee_not_approved_vacation: 18,
    reject_on_planning: 19,
    deleted_on_planning: 20,
    agreed_on_planning: 21,
    confirmation_with_schedule_done: 22,
    line_chief_updated_in_1c: 23,
    agreed_auto_process: 24,
    line_chief_changed: 25,
    agreed_after_exchange_1c: 26,
    notify_chief_about_auto_agreement: 27,
    notify_employee_about_auto_approval: 28,
    notify_employee_vacation_rejected: 29,
    line_chief_last_day_vacation_notification: 30,
    chief_agreed_vacation: 31,
    notify_chief_approve_vacation: 32,
    notify_chief_approve_now: 33,
    notify_employee_vacation_coming: 34,
    notify_employee_vacation_approved: 35,
    notify_employee_vacation_not_agreed: 36,
    notify_employee_confirmation_time_expired: 37,
    notify_chief_that_employee_planned_vacations: 38,
    notify_employee_that_planned_vacations_agreed: 39,
    notify_employee_that_planned_vacations_rejected: 40,
    notify_employee_that_chief_planned_vacations_for_him: 41,
    notify_employee_that_chief_changed_his_planned_vacations: 42,
    auto_reg_employee_vacation: 43,
    notify_employee_auto_reg_employee_vacation: 44,
    notify_chief_auto_reg_employee_vacation: 45,
    employee_consent_to_withhold_money: 46,
    notify_employee_vacation_dates_changed: 47,
    notify_employee_confirmation_time_expires_soon: 48,
    rejected_and_deleted: 49,
    rejected_on_rescheduling: 50,
    rejected_on_portal_creation: 51,
    agreed_on_creation: 52,
    created_as_new: 53,
    new_vacations_rejected_and_deleted: 54,
    employee_has_consent_to_withhold_money: 55,
    rejected_on_rescheduling_autoprocess: 56,
    agreed_on_rescheduling_autoprocess: 57,
    archived_after_reschedule_agreed_autoprocess: 58,
    notify_employee_about_inactive_reschedule: 59,
    notify_employee_advance_vacation_rejected: 60,
    notify_employee_unscheduled_vacation_rejected: 61,
    new_vacation_agreed_autoprocess: 62,
    new_vacation_rejected_autoprocess: 63,
    portal_cancellation: 64,
    rejected_on_cancellation: 65,
    agreed_on_cancellation: 66,
    removed_advance_days: 67,
    notify_cancellation_partial_approved: 77,
    notify_cancellation_approved: 78,
    notify_cancellation_rejected: 79,
    notify_cancellation_rejected_chief_not_have_time: 80,
    notify_chief_cancellation_need_to_approve_with_reschedule: 81,
    notify_chief_cancellation_need_to_approve_without_reschedule: 82,
    rejected_on_cancellation_without_reschedule: 83,
    agreed_on_cancellation_without_reschedule: 84,
    created_as_new_on_cancellation: 85,
    confirmation_employee_on_resubmission: 86,
    functional_chief_last_day_vacation_notification: 100,
    cancellation_in_progress: 200,
    cancellaton_finished: 201
  }.freeze
  RECALL_EVENTS = {
    portal_recall: 86,
    created_as_new_on_recall: 87,
    agreed_on_recall: 88,
    rejected_on_recall: 89,
    agreed_on_recall_without_reschedule: 90,
    rejected_on_recall_without_reschedule: 91,
    notify_chief_recall_need_to_approve_with_reschedule: 92,
    notify_recall_partial_approved: 93,
    notify_recall_approved: 94,
    notify_recall_rejected: 95
  }.freeze

  REPLACEMENT_EVENTS = {
    activate_vacation_approval_replacement: 98,
    deactivate_vacation_approval_replacement: 99
  }.freeze

  UNPAID_EVENTS = {
    notify_employee_unpaid_cancellation_approval: 300,
    notify_employee_unpaid_recall_approval: 301,
    notify_employee_unpaid_cancellation_partial_approval: 302,
    notify_employee_unpaid_recall_partial_approval: 303,
    notify_chiefs_about_unpaid_new_cancellation_vacation_for_approval: 304,
    notify_chiefs_about_unpaid_new_recall_vacation_for_approval: 305
  }.freeze

  # TODO: проверить как мерджится события замещения
  ALL_EVENTS = BASE_EVENTS.merge(RECALL_EVENTS, REPLACEMENT_EVENTS, UNPAID_EVENTS).freeze

  enum event: ALL_EVENTS

  enum status: { draft: 0, on_approval: 1, rejected: 2, agreed: 3, transfer_required: 4, created_order: 5,
                 transferred_as_payment: 6, used: 7, scheduled: 8, archived: 9, on_reschedule: 10, on_cancellation: 11,
                 cancelled: 12 }
end

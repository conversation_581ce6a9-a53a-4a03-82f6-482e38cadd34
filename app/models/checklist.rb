# frozen_string_literal: true

class Checklist < ApplicationRecord
  belongs_to :profile

  CHECKLIST_PERMITTED_PARAMS = %w[
    account_access_work_equipment account_access_programs account_access_email account_access_jira account_access_confluence
    account_access_redmine account_access_workspaces account_access_helpme account_access_directum introduction_adaptation_welcome_video
    introduction_adaptation_team introduction_adaptation_one_to_one introduction_adaptation_welcome_meeting
    introduction_adaptation_welcome_presentation introduction_adaptation_chat_welcome introduction_adaptation_chat_rostelecom
    introduction_adaptation_chat_work_chats labor_protection_olimpoks labor_protection_infobez portal_fill_profile portal_feedback
    portal_article portal_events after_probation_self_development after_probation_cafeteria
  ].freeze

  def self.permitted_params
    CHECKLIST_PERMITTED_PARAMS
  end

  def status
    active_deadline = profile.hire_date + 3.months + 1.week
    completed_deadline = active_deadline + 2.weeks

    if Date.current.before?(active_deadline)
      'active'
    elsif Date.current.between?(active_deadline, completed_deadline)
      'completed'
    else
      'hidden'
    end
  end
end

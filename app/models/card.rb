# frozen_string_literal: true

class Card < ApplicationRecord
  has_many_attached :images
  has_many :card_texts
  validate :check_image_size
  validate :check_image_format

  def check_image_size
    return unless images.attached?

    images.each do |img|
      next if img.blob.byte_size <= IMAGE_MAX_SIZE

      img.blob.purge if img.blob.persisted?
      message = I18n.t('activerecord.errors.card.image_size',
                       size: ActionController::Base.helpers.number_to_human_size(IMAGE_MAX_SIZE))
      errors.add(:images, message)
    end
  end

  def check_image_format
    return unless images.attached?

    images.each do |img|
      next if %w[image/png image/jpg image/jpeg].include?(img.blob.content_type)

      img.blob.purge if img.blob.persisted?
      message = I18n.t('activerecord.errors.card.image_format')
      errors.add(:images, message)
    end
  end
end

@import 'animations';
@import 'lib/editable';
@import 'blocks/top-menu';
@import 'blocks/dashboard';
@import 'blocks/channel-item';
@import 'blocks/gratitude-feeds';
@import 'elements';

body .container {
  margin-top: 1rem;
  display: block;
}

.alert {
  animation: fade-out .5s ease-in 3s forwards;
}

.alert.alert-warning {
  animation-delay: 6s;
}

.alert.alert-fixed {
  animation: none;
}

.clipboard-copy-selection {
  position: absolute;
  left: -9999px;

  & ~ .tooltip {
    top: -22px !important;
  }
}

.dir-view .dropdown-item {
  &:active, &:focus {
    color: white;
  }
}

[data-copied] {
  cursor: pointer;
  width: auto;
}

.header > .navbar {
  padding: 0.7rem 1rem;
}

.error {
  color: red;
}

.chars-count.text-right {
  text-align: right !important;
  width: 20%;
  float: right
}

.form-control-file {
  width: auto;
}

.compact-table {
  max-width: 600px;

  td, th {
    border: 0;
  }
}

@import url(https://fonts.googleapis.com/css?family=Open+Sans:400,700,600&subset=latin,cyrillic);

@font-face {
  font-family: "BasisGrotesquePro";
  src: url("../fonts/BasisGrotesquePro-Regular.woff") format("woff"),
    url("../fonts/BasisGrotesquePro-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "BasisGrotesquePro";
  src: url("../fonts/BasisGrotesquePro-Light.woff") format("woff"),
    url("../fonts/BasisGrotesquePro-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "BasisGrotesquePro";
  src: url("../fonts/BasisGrotesquePro-Bold.woff") format("woff"),
    url("../fonts/BasisGrotesquePro-Bold.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

.glyphicon.glyphicon-ok {
  @extend .fa;
  @extend .fa-check;
}

@each $size in 14, 16, 18 {
  .fs-#{$size} {
    font-size: #{$size}px;
  }
}

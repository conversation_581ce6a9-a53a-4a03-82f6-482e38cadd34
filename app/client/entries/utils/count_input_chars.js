import * as $ from 'jquery';

export default function countInputChars (element) {
  $(element).each(function () {
    const $self = $(this);
    $self.parent().find('.chars-count').remove();
    $self.wrap('<div></div>')
      .parent()
      .append('<div class="chars-count text-right"></div>');
    changeAllowedChars($self);
    $self.bind('input', () => changeAllowedChars($self));
  });

  function changeAllowedChars ($inputField) {
    const maximumCharsAllowed = $inputField.attr('maxlength');
    const inputChars = $inputField.val().length;
    $inputField.siblings('.chars-count').text(`${inputChars}/${maximumCharsAllowed}`);
  }
};

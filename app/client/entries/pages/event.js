/* global SetkaEditor */

import initPageForm from '../components/init_page_form';
import SetkaHandler from '../components/setka_editor';
import UnsafeLeavingPreventer from '../components/unsaved_leaving_preventer';
import countInputChars from '../utils/count_input_chars';

export default class Event {
  constructor () {
    initPageForm('event');
    new UnsafeLeavingPreventer();

    const status = $('#event_status').val();

    const currentDate = new Date();
    const startDate = new Date(Date.parse($('#event_scheduled_at').val().replace('+0000', 'Z')));

    if (startDate >= currentDate) {
      $('#startDate').datetimepicker({
        defaultDate: startDate,
        minDate: currentDate
      });
    } else {
      if (status === 'published') {
        $('#startDate').datetimepicker({
          defaultDate: startDate,
          disable: true
        });
      } else {
        $('#startDate').datetimepicker({
          defaultDate: currentDate,
          minDate: currentDate
        });
      }
    }

    let endDate = $('#event_ended_at').val().replace('+0000', 'Z');
    if (endDate === '') { endDate = startDate; }
    endDate = new Date(endDate);
    if (endDate >= currentDate) {
      $('#endDate').datetimepicker({
        useCurrent: false,
        defaultDate: endDate,
        minDate: startDate
      });
    } else {
      if (status === 'published') {
        $('#endDate').datetimepicker({
          useCurrent: false,
          defaultDate: endDate,
          disable: true
        });
      } else {
        const setDate = currentDate.setHours(currentDate.getHours() + 8);
        $('#endDate').datetimepicker({
          useCurrent: false,
          defaultDate: setDate,
          minDate: startDate
        });
      }
    }

    this.selectEventCategory = this.selectEventCategory.bind(this);
    this.setVisibilityForElements = this.setVisibilityForElements.bind(this);

    this.setkaContainer = $('.js-stk-editor');
    this.citiesContainer = $('.js-events-cities');
    this.speakersContainer = $('.js-events-users').select2({
      placeholder: 'Спикер мероприятия',
      maximumSelectionLength: 1,
      tags: true
    });
    this.eventCategories = $('#event_category');
    const label = this.eventCategories.val();
    this.setVisibilityForElements(label);
    this.eventCategories.on('change', this.selectEventCategory);

    $('#startDate').on('change.datetimepicker', this.changeDates);

    // Если нет селекта городов, значит есть только селект с офисами.
    // В этом случае просто инициализируем его, беря список офисов из отрендереннго html'я
    if (this.citiesContainer.length > 0) {
      this.citiesContainer.trigger('change');
      this.citiesContainer.select2(this.citiesSelectOptions);
    }

    countInputChars('#event_description');
    $('#btnSubmit').click(() => {
      $('#event_scheduled_at').val(this.convertDate($('#startDate')).toISOString());

      const category = $('#event_category').val();
      const isSkipEndedAt = ['cracker', 'corporate', 'live'].includes(category);
      if (isSkipEndedAt) {
        $('#event_ended_at').val('');
      } else {
        $('#event_ended_at').val(this.convertDate($('#endDate')).toISOString());
      }
    });
  }

  changeDates (e) {
    $('#endDate').datetimepicker('useCurrent', true);
    $('#endDate').datetimepicker('minDate', new Date(e.date));
  }

  selectEventCategory (e) {
    const label = $(e.target).val();
    this.setVisibilityForElements(label);
  }

  setVisibilityForElements (label) {
    switch (label) {
      case 'conference':
        $('#speaker, #room, #duration, #registration').hide();
        $('#banner, #address, #end-calendar, #site, #map').show();
        break;
      case 'cracker':
        $('#banner, #address, #end-calendar, #site, #map').hide();
        $('#speaker, #room, #duration, #registration').show();
        break;
      case 'activity':
        $('#speaker, #site, #room, #duration').hide();
        $('#banner, #end-calendar, #map, #address, #registration').show();
        break;
      case 'live':
        $('#site, #room, #end-calendar, #address, #map, #registration, #speaker').hide();
        $('#banner, #duration').show();
        break;
      case 'corporate':
        $('#speaker, #end-calendar, #room, #registration').hide();
        $('#site, #address, #banner, #map, #duration').show();
        break;
    }
    this.placeholderSelect(label);
    this.citiesContainer.select2(this.citiesSelectOptions);
    if ($('#event_is_body').val() === 'false') {
      this.reloadSetka(label);
    }
  }

  convertDate (date) {
    return new Date($(date).data().date.replace(/(\d+).(\d+).(\d+)/, '$3/$2/$1'));
  }

  placeholderSelect (label) {
    const isMultipleCities = ['activity', 'corporate'].includes(label);
    this.citiesSelectOptions = {
      maximumSelectionLength: isMultipleCities ? Infinity : 1,
      placeholder: (isMultipleCities || label === 'conference') ? 'Мероприятие будет опубликовано для всех городов' : 'Город проведения мероприятия'
    };
  }

  reloadSetka (label) {
    this.setSetkaTemplate(label);

    SetkaEditor.stop();
    const editorContainer = document.querySelector('.js-stk-editor');

    if (!editorContainer.classList.contains('stk-editor')) {
      editorContainer.classList.add('stk-editor');
      editorContainer.id = 'setka-editor';
    }

    new SetkaHandler(editorContainer);
  }

  setSetkaTemplate (label) {
    this.$setkaContainer = $(this.setkaContainer[0]);
    const labelsToActions = {
      conference: this.conferenceSetkaTemplate.bind(this),
      cracker: this.crackerSetkaTemplate.bind(this),
      activity: this.activitySetkaTemplate.bind(this),
      live: this.liveSetkaTemplate.bind(this),
      corporate: this.corporateSetkaTemplate.bind(this)
    };
    labelsToActions[label]();
  }

  conferenceSetkaTemplate () {
    this.$setkaContainer.attr({
      'data-themeId': $('#event_conference_template_id').val(),
      'data-body': $('#event_conference_body').val()
    });
  }

  crackerSetkaTemplate () {
    this.$setkaContainer.attr({
      'data-themeId': $('#event_template_id').val(),
      'data-body': $('#event_cracker_body').val()
    });
  }

  activitySetkaTemplate () {
    this.$setkaContainer.attr({
      'data-themeId': $('#event_template_id').val(),
      'data-body': $('#event_activity_body').val()
    });
  }

  liveSetkaTemplate () {
    this.$setkaContainer.attr({
      'data-themeId': $('#event_template_id').val(),
      'data-body': $('#event_live_body').val()
    });
  }

  corporateSetkaTemplate () {
    this.$setkaContainer.attr({
      'data-themeId': $('#event_template_id').val(),
      'data-body': $('#event_corporate_body').val()
    });
  }
}

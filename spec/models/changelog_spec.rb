# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Changelog, type: :model do
  let(:subject) { described_class.new(title: Faker::Lorem.unique.sentence(word_count: 3), body: Faker::Lorem.paragraphs) }

  describe 'Validations' do
    it 'is not valid without a title' do
      subject.title = ''
      expect(subject).to_not be_valid
    end

    it 'is not valid with long title' do
      subject.title = 'a' * 100
      expect(subject).to_not be_valid
    end

    it 'is not valid without a body' do
      subject.body = ''
      expect(subject).to_not be_valid
    end
  end
end

# frozen_string_literal: true

FactoryBot.define do
  factory :vacation_planning do
    vacation_type { '7a74ec83-abd7-11e6-b039-98eecb23e9b1' }
    days { 28 }
    planning_data_received do
      Date.parse format('%<d_m>s.%<year>s',
                        d_m: VACATION_PLANNING_START_DATE,
                        year: Date.current.prev_year.year)
    end
    planning_year { Date.current.year }

    association :profile, factory: :profile
  end
end

# frozen_string_literal: true

FactoryBot.define do
  factory :profile do
    email { "#{Faker::Name.first_name}.#{Faker::Name.last_name}@rt.ru" }
    first_name { Faker::Name.first_name }
    last_name { Faker::Name.last_name }
    phone { Faker::Number.number(digits: 11) }
    phone_verification_date { Faker::Date.backward(days: 7) }
    have_schedule { true }

    hire_date { Faker::Date.backward(days: 1000) }

    available_vacation_days { 0 }

    association :office, factory: :office
    association :department, factory: :department

    after(:build) do |p|
      available_vacation_days_details = {
        'past_periods' => {
          'types' => {
            '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 14,
            'd3249cac-c5be-11e6-823e-0050560333d3' => 3,
            'a6556864-ad87-11e6-be96-98eecb23e9b1' => 8
          },
          'dates' => {
            'start_date' => '2017-05-03',
            'end_date' => Date.current.change(month: 5, day: 3).to_s(:db)
          }
        },
        'current_periods' => {
          'types' => {
            '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
            'd3249cac-c5be-11e6-823e-0050560333d3' => 2,
            'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
          },
          'dates' => {
            'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
            'end_date' => Date.current.change(month: 5, day: 2).to_s(:db)
          }
        },
        'used_periods' => {
          'types' => {
            '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
            'd3249cac-c5be-11e6-823e-0050560333d3' => 1,
            'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
          },
          'dates' => {
            'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
            'end_date' => Date.current.to_s(:db)
          }
        },
        'available_today_days' => {
          'types' => {
            '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 14,
            'd3249cac-c5be-11e6-823e-0050560333d3' => 4,
            'a6556864-ad87-11e6-be96-98eecb23e9b1' => 8
          },
          'dates' => {
            'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
            'end_date' => Date.current.to_s(:db)
          }
        },
        'available_end_year_days' => {
          'types' => {
            '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 16,
            'd3249cac-c5be-11e6-823e-0050560333d3' => 4,
            'a6556864-ad87-11e6-be96-98eecb23e9b1' => 9
          },
          'dates' => {
            'start_date' => Date.current.to_s(:db),
            'end_date' => Date.current.end_of_year.to_s(:db)
          }
        }
      }
      p.available_vacation_days_details = available_vacation_days_details
    end

    trait :has_past_periods_positive_days do
      after(:build) do |p|
        data = {
          'past_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 14,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 3,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 8
            },
            'dates' => {
              'start_date' => '2017-05-03',
              'end_date' => Date.current.change(month: 5, day: 3).to_s(:db)
            }
          },
          'current_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 2,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.change(month: 5, day: 2).to_s(:db)
            }
          },
          'used_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 1,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_today_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 14,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 3,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 8
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_end_year_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 16,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 4,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 9
            },
            'dates' => {
              'start_date' => Date.current.to_s(:db),
              'end_date' => Date.current.end_of_year.to_s(:db)
            }
          }
        }
        p.available_vacation_days_details = data
      end
    end

    trait :not_allowed_advance_but_allowed_past_periods do
      after(:build) do |p|
        data = {
          'past_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => -14,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 3,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 3
            },
            'dates' => {
              'start_date' => '2017-05-03',
              'end_date' => Date.current.change(month: 5, day: 3).to_s(:db)
            }
          },
          'current_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 2,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.change(month: 5, day: 2).to_s(:db)
            }
          },
          'used_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 1,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_today_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 0,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 4,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 8
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_end_year_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 2,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 4,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 9
            },
            'dates' => {
              'start_date' => Date.current.to_s(:db),
              'end_date' => Date.current.end_of_year.to_s(:db)
            }
          }
        }
        p.available_vacation_days_details = data
      end
    end

    trait :not_allowed_advance_no_past_periods do
      after(:build) do |p|
        data = {
          'past_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => -14
            },
            'dates' => {
              'start_date' => '2017-05-03',
              'end_date' => Date.current.change(month: 5, day: 3).to_s(:db)
            }
          },
          'current_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 2,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.change(month: 5, day: 2).to_s(:db)
            }
          },
          'used_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 1,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_today_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 0,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 1,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 0
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_end_year_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 2,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 2,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 2
            },
            'dates' => {
              'start_date' => Date.current.to_s(:db),
              'end_date' => Date.current.end_of_year.to_s(:db)
            }
          }
        }
        p.available_vacation_days_details = data
      end
    end

    trait :allowed_advance_has_available_today_24_days_without_schedule do
      after(:build) do |p|
        data = {
          'past_periods' => {
            'types' => {
            },
            'dates' => {
              'start_date' => '2017-05-03',
              'end_date' => Date.current.change(month: 5, day: 3).to_s(:db)
            }
          },
          'current_periods' => {
            'types' => {
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.change(month: 5, day: 2).to_s(:db)
            }
          },
          'used_periods' => {
            'types' => {
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_today_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 12,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 4,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 8
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_end_year_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 12,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 4,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 8
            },
            'dates' => {
              'start_date' => Date.current.to_s(:db),
              'end_date' => Date.current.end_of_year.to_s(:db)
            }
          }
        }
        p.available_vacation_days_details = data
        p.have_schedule = false
      end
    end

    trait :has_past_periods_additional_vacation_days_no_main_days do
      after(:build) do |p|
        data = {
          'past_periods' => {
            'types' => {
              'd3249cac-c5be-11e6-823e-0050560333d3' => 3
            },
            'dates' => {
              'start_date' => '2017-05-03',
              'end_date' => Date.current.change(month: 5, day: 3).to_s(:db)
            }
          },
          'current_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 2,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.change(month: 5, day: 2).to_s(:db)
            }
          },
          'used_periods' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 21,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 1,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 6
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_today_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 14,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 4,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 8
            },
            'dates' => {
              'start_date' => Date.current.last_year.change(month: 5, day: 3).to_s(:db),
              'end_date' => Date.current.to_s(:db)
            }
          },
          'available_end_year_days' => {
            'types' => {
              '7a74ec83-abd7-11e6-b039-98eecb23e9b1' => 16,
              'd3249cac-c5be-11e6-823e-0050560333d3' => 4,
              'a6556864-ad87-11e6-be96-98eecb23e9b1' => 9
            },
            'dates' => {
              'start_date' => Date.current.to_s(:db),
              'end_date' => Date.current.end_of_year.to_s(:db)
            }
          }
        }
        p.available_vacation_days_details = data
      end
    end
  end
end

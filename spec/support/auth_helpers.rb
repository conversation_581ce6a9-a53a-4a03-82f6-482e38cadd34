# frozen_string_literal: true

module Auth
  module SmsVerificationHelpers
    def stub_sms_verification_hash!(params = {})
      if params[:code_expired?].present?
        allow_any_instance_of(AuthCode::Store).to receive(:code_expired?).and_return(params[:code_expired?])
      end

      if params[:many_code_confirms].present?
        allow_any_instance_of(AuthCode::Store).to receive(:many_code_confirms?).and_return(params[:many_code_confirms])
      end

      if params[:code].present?
        allow_any_instance_of(AuthCode::Store).to receive(:[]).with(:code).and_return(params[:code])
      end

      if params[:code_expired].present?
        allow_any_instance_of(AuthCode::Store).to receive(:[]).with(:code_expired).and_return(params[:code_expired])
      end

      if params[:code_confirmed].present?
        allow_any_instance_of(AuthCode::Store).to receive(:[]).with(:code_confirmed).and_return(params[:code_confirmed])
      end
    end
  end
end

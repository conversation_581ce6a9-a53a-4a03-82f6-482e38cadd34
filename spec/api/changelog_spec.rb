# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ChangelogAPI do
  describe 'Get changelog list' do
    before(:each) do
      create_and_sign_in_with_account
    end
    let!(:endpoint) { '/api/changelogs' }

    it 'returns list of published changelogs' do
      create_list(:changelog, 5)
      create_list(:changelog, 3, status: 1)

      get endpoint
      expect(JSON.parse(response.body).size).to eql 3
    end

    it 'doesn`t return list of draft changelogs' do
      create_list(:changelog, 5)

      get endpoint
      expect(JSON.parse(response.body).size).to eql 0
    end

    it 'returns limited total changelogs' do
      create_list(:changelog, ITEM_LIST_DEFAULT_PER_PAGE + 5, status: 1)

      get endpoint
      expect(JSON.parse(response.body).size).to eql ITEM_LIST_DEFAULT_PER_PAGE
    end
  end

  describe 'GET changelog' do
    context 'set changelogs views' do
      before(:each) do
        create_and_sign_in_with_account
      end
      let!(:endpoint) { '/api/changelogs/view' }

      it 'returns http success 200 ' do
        patch endpoint
        expect(response.status).to eq(200)
      end

      it 'sets views to published changelogs ' do
        changelog_draft = create(:changelog)
        changelog_published = create(:changelog, status: 1)

        patch endpoint
        expect(response.status).to eq(200)
        expect(changelog_draft.views.size).to eql 0
        expect(changelog_published.views.size).to eql 1
      end

      it 'sets views to all published changelogs' do
        changelog_published1 = create(:changelog, status: 1)
        changelog_published2 = create(:changelog, status: 1)

        patch endpoint
        expect(response.status).to eq(200)
        expect(changelog_published1.views.size).to eql 1
        expect(changelog_published2.views.size).to eql 1
      end
    end
  end
end

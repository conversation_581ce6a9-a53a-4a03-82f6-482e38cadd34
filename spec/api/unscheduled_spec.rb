# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UnscheduledAPI do
  using Overrides

  let(:params) do
    {
      code: 'abc123',
      functional_chief_ids: create(:profile).id,
      advance: 3,
      vacations: [{
        vacation_type: '7a74ec83-abd7-11e6-b039-98eecb23e9b1',
        start_date: 10.days.from_now.to_date.iso8601,
        days: 2
      }, {
        vacation_type: '7a74ec83-abd7-11e6-b039-98eecb23e9b1',
        start_date: 20.days.from_now.to_date.iso8601,
        days: 2
      }, {
        vacation_type: '7a74ec83-abd7-11e6-b039-98eecb23e9b1',
        start_date: 13.days.from_now.to_date.iso8601,
        days: 1
      }]
    }
  end

  context 'chief' do
    before(:each) do
      @account = create_and_sign_in_with_account
      @profile = build(:profile)
      @profile.update(parent: @account.profile)

      @vacation = create(:vacation_period, start_date: DateTime.now + 1, end_date: DateTime.now + 7, days: 7,
                                           status: VacationPeriod.statuses[:on_approval], source_type: :portal_creation,
                                           vacation_schedule_type: :without_schedule, profile: @profile)

      AssignedVacationChief.find_by_period_id_and_is_line_chief(@vacation.id, true).destroy
      @vacation_two = create(:vacation_period, start_date: DateTime.now + 8, end_date: DateTime.now + 14, days: 7,
                                               status: VacationPeriod.statuses[:on_approval], source_type: :portal_creation,
                                               vacation_schedule_type: :without_schedule, profile: @profile)

      AssignedVacationChief.find_by_period_id_and_is_line_chief(@vacation_two.id, true).destroy
      @collection = VacationCollection.create!(profile: @profile, vacation_periods: [@vacation, @vacation_two])

      # NOTE: небольшой хак для использование наших собственных факториз, вместо факториз по умолчаню
      @vacation.assigned_vacation_chiefs.destroy_all
      @vacation_two.assigned_vacation_chiefs.destroy_all

      @vacation.assigned_vacation_chiefs.create(chief_id: @account.profile.id, is_line_chief: true)
      @vacation_two.assigned_vacation_chiefs.create(chief_id: @account.profile.id, is_line_chief: true)
      # NOTE: конец хака :)

      @functional_chief_first = build(:profile)
      @functional_chief_second = build(:profile)

      @vacation.chiefs << @functional_chief_first
      @vacation.chiefs << @functional_chief_second
      @vacation.assigned_vacation_chiefs << AssignedVacationChief.create(period_id: @vacation.id, chief_id: @account.profile.id, is_line_chief: true)
      @vacation_two.chiefs << @functional_chief_first
      @vacation_two.chiefs << @functional_chief_second
      @vacation_two.assigned_vacation_chiefs << AssignedVacationChief.create(period_id: @vacation_two.id, chief_id: @account.profile.id, is_line_chief: true)
    end

    describe 'Отклонение руководителем отпуска вне графика' do
      let!(:reject_vacations_url) { "/api/unscheduled/chief/#{@vacation.id}/reject" }
      subject { patch reject_vacations_url, params: { comment: 'some comment' } }

      context 'Добавили аванс отпуску' do
        before(:each) do
          @advance = ProfileVacationAdvance.create!(profile: @profile, vacation_period: @vacation, days: 7, year: Date.today.year)
        end

        it 'долно вернуться один отпуск ' do
          subject
          expect(json_response['vacations'].size).to eq(1)
        end
      end

      context 'Если отпуск авансом и единственный, то удаляем его' do
        before(:each) do
          ProfileVacationAdvance.create!(profile: @profile, vacation_period: @vacation, days: 1, year: Date.today.year)
          ProfileVacationAdvance.create!(profile: @profile, vacation_period: @vacation_two, days: 1, year: Date.today.year)
        end

        it 'should 200' do
          subject
          expect(response.status).to eq(200)
        end

        it 'response empty' do
          subject
          expect(json_response).to eq(nil)
          expect(response.status).to eq(200)
        end
      end

      context 'Когда руководитель отклоняект отпуск без авансов' do
        it 'должно вернуть 200' do
          subject
          expect(response.status).to eq(200)
        end

        it 'должно вернуть два отклоненных отпуска' do
          subject
          expect(json_response['vacations'].size).to eq(2)
        end

        it 'должно вернуть в отклоненном статусе' do
          subject
          @vacation_two.reload
          expect(@vacation_two.rejected?).to be true
        end
      end
    end

    describe 'руководитель получает данные по отпускам вне графика, которые нужно согласовать' do
      let(:get_vacations_url) { "/api/unscheduled/chief/#{@vacation.id}/vacations" }
      subject { get get_vacations_url }

      context 'у отпуска есть про-эскалированный руководитель' do
        it 'не должен блокировать эскалацию' do
          @escalated_line_chief = create(:profile)

          account = build(:account)
          account.skip_confirmation_notification!
          account.confirm
          account.save!
          account.update(profile_id: @escalated_line_chief.id)

          @account.profile.update!(parent_id: @escalated_line_chief.id)

          @vacation.escalate_line_chief!

          @vacation.reload
          expect(@vacation.assigned_vacation_chiefs.line_chiefs.count).to eq(2)

          sign_out @account
          sign_in account

          subject

          expect(response.code.to_i).to eq(200)
        end
      end

      context 'просмотр деталей отпусков' do
        it 'should be 3 chiefs' do
          subject
          expect(json_response['chiefs'].size).to eq(3)
        end

        it 'должно быть два отпуска' do
          subject
          expect(json_response['vacations'].size).to eq(2)
        end

        it 'отпуска должны быть на согласовании' do
          subject
          expect(json_response['vacations'].first['status']).to eq(@vacation.reload.status)
        end
      end
    end
  end

  context 'Сотрудник оформляет отпуск вне графика' do
    before(:each) do
      @account = create_and_sign_in_with_account
      @profile = @account.profile
      @profile.update(available_vacation_days: 26, have_schedule: 1)
      @calculator = VacationNewDateCalculator.new(@profile)
    end

    describe 'GET даты для составления Нового желаемого отпуска, при наличии графика' do
      before(:each) do
        chief = build(:profile)
        @profile.update(parent: chief)
      end

      let(:new_vacations_url) { '/api/unscheduled/employee/new' }

      subject { get new_vacations_url }

      xit 'возвращает даты для желаемого отпуска при наличии остатка дней за прошлые периоды' do
        subject
        expect(json_response['start_date']).to eq(11.working_days.from_now.to_date.iso8601)
        expect(json_response['end_date']).to eq(31.days.from_now.to_date.iso8601)
      end

      it 'вернет данные об остатке аванса' do
        subject
        expect(json_response['allowed_advance_days']).to eq(@profile.advance_calculator.max_allowed_advance)
      end

      it 'вернет доступные остатки по типам отпусков ' do
        subject
        expect(json_response['available_days'].sort).to eq(@calculator.available_days.sort)
      end
    end

    describe 'Проверяем, что нельзя оформить вне графика, если нет дней' do
      before(:each) do
        profile = build(:profile, :not_allowed_advance_no_past_periods)
        @account.update(profile: profile)
      end
      let(:new_vacations_url) { '/api/unscheduled/employee/new' }

      subject { get new_vacations_url }

      it 'should 403' do
        subject
        expect(response.code.to_i).to eq(403)
      end
    end

    describe 'Проверяем, что не разрешено оформлять, если нет линейного' do
      let(:new_vacations_url) { '/api/unscheduled/employee/new' }

      subject { get new_vacations_url }

      before(:each) do
        expect(@profile).to receive(:general_manager?).and_return(false)
      end

      it 'should 403' do
        subject
        expect(response.code.to_i).to eq(403)
      end
    end

    describe 'Проверяем руководителей в ответе апи нового отпуска' do
      before(:each) do
        @functional_chief_first = build(:profile)
        @functional_chief_second = build(:profile)
        @functional_chief_third = build(:profile)
        @vp = create(:vacation_period, profile: @profile, start_date: 10.days.ago, end_date: 5.days.ago)
        @vp.chiefs << @functional_chief_first
        @vp.chiefs << @functional_chief_second
        @vp.chiefs << @functional_chief_third
        chief = build(:profile)
        @profile.update(parent: chief)
      end

      let(:new_vacations_url) { '/api/unscheduled/employee/new' }

      subject { get new_vacations_url }
      it 'вернет руководителей' do
        subject
        expect(json_response['chiefs'].size).to eq(3)
      end

      it 'вернет максимум двух функциональных' do
        subject
        functional_chiefs = json_response['chiefs'].select { |chief| chief['is_line_chief'] == false }
        expect(functional_chiefs.size).to eq(2)
      end

      it 'вернет линейного руководителя' do
        subject
        line_chiefs = json_response['chiefs'].select { |chief| chief['is_line_chief'] == true }
        expect(line_chiefs.size).to eq(1)
      end
    end
  end

  describe 'POST #verify_and_reschedule' do
    let(:verify_and_reschedule_url) { '/api/unscheduled/employee/' }

    subject { post verify_and_reschedule_url, params: params }

    before(:each) do
      allow(Setting).to receive(:find_by).with(name: CODE_BY_EMAIL_SETTING).and_return(Setting.new)
      allow_any_instance_of(Setting).to receive(:mode).and_return(false)
      allow(AuthCode::SmsSender).to receive(:send).and_return(true)

      @account = create_and_sign_in_with_account
      @profile = @account.profile

      chief = create(:profile, department_id: @profile.department_id)
      @profile.update(parent_id: chief.id)

      allow_any_instance_of(Profile).to receive(:general_manager?).and_return(false)

      @store = AuthCode::Store.new(id: @profile.id)
      @store.save_code('abc123')
    end

    context 'когда все дни взяты авансом' do
      it 'отдает ответ 201, и создает все отпуска из авансовых дней' do
        params[:advance] = 5
        post verify_and_reschedule_url, params: params
        expect(response.code.to_i).to eq(201)
        expect(json_response['advance_sum']).to eq(5)
        expect(json_response['vacations'][0]['advance']).to eq(2)
        expect(json_response['vacations'][1]['advance']).to eq(1)
        expect(json_response['vacations'][2]['advance']).to eq(2)
      end
    end

    context 'когда никаких дней не взято авансом' do
      it 'отдает ответ 201 и не создает отпуска авансом' do
        q_params = params.slice(:code, :functional_chief_ids, :vacations)
        post verify_and_reschedule_url, params: q_params
        expect(response.code.to_i).to eq(201)
        expect(json_response['advance_sum']).to eq(0)
        expect(json_response['vacations'][0]['advance']).to eq(0)
      end
    end

    it 'создает и возвращает коллекцию отпусков вне графика' do
      subject

      expect(json_response.keys.sort).to eq(%w[id deadline_date chiefs children_count vacations status comment whois advance_sum can_resubmit].sort)
      expect(json_response['status']).to eq('on_approval')
      expect(json_response['advance_sum']).to eq(3)
      expect(@profile.vacation_collections.where(vacation_schedule_type: VacationCollection.vacation_schedule_types[:without_schedule]).count).to eq(1)
      expect(@profile.vacation_collections.first.vacation_periods.count).to eq(3)

      # NOTE: vacation logger created
      vac_id = @profile.vacation_collections.first.vacation_periods.first.id
      expect(VacationHistory.where(vacation_period_id: vac_id, event: :created_as_new).count).to eq(1)

      # NOTE: vacation confirmation logger created
      # не корректный тест, т.к. для эндпоинта '/api/unscheduled/employee/verify_and_create' в API::VacationScheduleHelpers не логируется 'event: :agreed_on_creation'
      # expect(VacationHistory.where(vacation_period_id: vac_id, event: :agreed_on_creation).count).to eq(1)
    end
  end

  describe 'PUT /move_vacations' do
    before(:each) do
      allow(Setting).to receive(:find_by).with(name: CODE_BY_EMAIL_SETTING).and_return(Setting.new)
      allow_any_instance_of(Setting).to receive(:mode).and_return(false)
      allow(AuthCode::SmsSender).to receive(:send).and_return(true)

      @account = create_and_sign_in_with_account
      @profile = @account.profile

      chief = create(:profile, department_id: @profile.department_id)
      @profile.update(parent_id: chief.id)

      allow_any_instance_of(Profile).to receive(:general_manager?).and_return(false)

      vc = VacationCollection.create!(profile: @profile, vacation_schedule_type: VacationCollection.vacation_schedule_types[:without_schedule])
      @vacation = create(:vacation_period, vacation_collection_id: vc.id, status: :rejected, profile_id: @profile.id)

      funct_chief = create(:profile, department_id: @profile.department_id)
      AssignedVacationChief.create!(period_id: @vacation.id, chief_id: funct_chief.id, is_line_chief: false)

      @store = AuthCode::Store.new(id: @profile.id)
      @store.save_code('abc123')
    end

    let(:vacation_url) { "/api/unscheduled/employee/#{@vacation.id}/move_vacations" }
    subject { put vacation_url, params: params }

    it 'should respond with 200' do
      subject
      expect(response.code.to_i).to eq(200)
      @profile.reload
      expect(@profile.vacation_periods.count).to eq(3)
      expect(json_response['chiefs'].count).to eq(2)
    end
  end

  describe 'GET vacations' do
    before(:each) do
      allow(Setting).to receive(:find_by).with(name: CODE_BY_EMAIL_SETTING).and_return(Setting.new)
      allow_any_instance_of(Setting).to receive(:mode).and_return(false)
      allow(AuthCode::SmsSender).to receive(:send).and_return(true)

      @account = create_and_sign_in_with_account
      @profile = @account.profile

      chief = create(:profile, department_id: @profile.department_id)
      @profile.update(parent_id: chief.id)

      allow_any_instance_of(Profile).to receive(:general_manager?).and_return(false)

      vc = VacationCollection.create!(profile: @profile, vacation_schedule_type: VacationCollection.vacation_schedule_types[:without_schedule])
      @vacation = create(:vacation_period,
                         vacation_collection_id: vc.id,
                         status: :on_approval,
                         source_type: :portal_creation,
                         vacation_schedule_type: VacationPeriod.vacation_schedule_types[:without_schedule],
                         start_date: 7.days.from_now,
                         end_date: 10.days.from_now,
                         vacation_type: 'd3249cac-c5be-11e6-823e-0050560333d3',
                         profile_id: @profile.id)
    end

    let(:vacation_url) { "/api/unscheduled/employee/#{@vacation.id}/vacations" }
    subject { get vacation_url }

    it 'should response with 200' do
      subject
      expect(response.code.to_i).to eq(200)
    end
  end
end

# frozen_string_literal: true

RSpec.describe OfficesAPI do
  before(:each) do
    create_and_sign_in_with_account
  end

  describe 'GET offices' do
    before(:each) do
      @city1 = create(:city)
      @city2 = create(:city)
      @office1 = create(:office, name: Faker::Lorem.unique.sentence(word_count: 1), city_id: @city1.id)
      get '/api/offices'
    end

    it 'return http success 200' do
      expect(response.status).to eq(200)
    end

    it 'return hash in JSON format' do
      expect(JSON.parse(response.body)).to be_instance_of(Array)
    end

    it 'return city with offices' do
      body = JSON.parse(response.body)
      expect(body[0]['city_id']).to eq(@city1.id)
      expect(body[0]).to include('offices')
    end

    it 'don`t return city without offices' do
      expect(JSON.parse(response.body)[1]).to eq(nil)
    end
  end
end

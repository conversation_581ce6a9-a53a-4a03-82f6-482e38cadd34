# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PermissionsAPI do
  before(:each) do
    VacationStagesData.create!
    @role = Role.first_or_create!(name: :hr_planner)
    @role.role_permissions << RolePermission.new(code: :hr_planner)
    @chief = create_and_sign_in_with_account
  end

  describe 'GET hr planner for vacations' do
    let(:permission_url) { '/api/permissions' }

    subject { get permission_url, params: { scope: 'is_hr_planner_allowed', action: 'show' } }

    it 'should return 403 for a regular user' do
      expect(subject).to eq 403
    end

    context 'when the user is a hr planner' do
      before do
        @chief.update!(role_id: @role.id)
      end

      context 'when outside of hr planner stage' do
        it 'should return 403' do
          VACATION_HR_PLANNER_START_DATE = 2.days.ago.beginning_of_month.strftime('%d.%m')
          VACATION_HR_PLANNER_END_DATE = 2.days.ago.beginning_of_month.strftime('%d.%m')

          expect(subject).to eq 403
        end
      end

      context 'when hr planner stage is specified' do
        it 'should return 403' do
          VACATION_HR_PLANNER_START_DATE = 2.days.ago.beginning_of_month.strftime('%d.%m')
          VACATION_HR_PLANNER_END_DATE = 2.days.from_now.end_of_month.strftime('%d.%m')

          expect(subject).to eq 200
        end
      end
    end
  end

  describe 'GET view permissions for vacations' do
    let(:permission_url) { '/api/permissions' }

    subject { get permission_url, params: { scope: 'vacation_periods', action: 'show' } }

    context 'chief view' do
      before(:each) do
        @employee = create(:account)
        @employee.skip_confirmation_notification!
        @employee.confirm
        @employee.save!

        e_profile = @employee.profile
        e_profile.update!(parent_id: @chief.profile.id)
      end

      it 'returns positive response' do
        subject
        expect(response.code).to eq('200')
      end
    end

    context 'non authorzed view' do
      it 'returns 403 unauthorized code' do
        subject
        expect(response.code).to eq('403')
      end
    end

    context 'wrong context' do
      subject { get permission_url, params: { scope: 'SOMETHING_DIFFERENT', action: 'show' } }

      it 'returns 422 response code' do
        subject
        expect(response.code).to eq('422')
      end
    end
  end

  describe 'GET permissions to show cards' do
    let(:permission_url) { '/api/permissions' }

    subject { get permission_url, params: { scope: 'card_available_for_users', action: 'show' } }

    it 'returns positive response' do
      create(:card, mode: true)
      subject
      expect(response.code).to eq('200')
    end

    it 'returns 403 unauthorized code' do
      create(:card, mode: false)
      subject
      expect(response.code).to eq('403')
    end
  end

  describe 'GET permissions to create cards' do
    let(:permission_url) { '/api/permissions' }

    subject { get permission_url, params: { scope: 'card_available_for_users', action: 'create' } }

    it 'returns 200' do
      create(:card, mode: true)
      @chief.profile.update!(number_cards_mail: 17, cards_sent_at: DateTime.yesterday)
      subject
      expect(response.code).to eq('200')
    end

    it 'return success 200' do
      create(:card, mode: true)
      @chief.profile.update!(number_cards_mail: 2, cards_sent_at: DateTime.now)
      subject
      expect(response.code).to eq('200')
    end

    it 'returns 403 unauthorized code' do
      create(:card, mode: true)
      @chief.profile.update!(number_cards_mail: 15, cards_sent_at: DateTime.now)
      subject
      expect(response.code).to eq('403')
    end

    it 'returns 403 unauthorized code' do
      create(:card, mode: false)
      subject
      expect(response.code).to eq('403')
    end
  end

  describe 'GET permissions to display 1C button' do
    let(:permission_url) { '/api/permissions' }
    # let(:setting) { Setting.where(name: FEEDBACK_BANNER).first_or_create }
    before do
      sign_out @chief

      @chief.profile.children.create!(last_name: 'test', first_name: 'test', department_id: Department.first.id)

      @replacement = create_and_sign_in_with_account

      @replacement.profile.undertaken_schedules.create!(
        requested_profile_id: @chief.profile.id,
        status: :activated,
        start_date: 2.days.ago
      )

      allow_any_instance_of(VacationReadyToSubmitPolicy).to receive(:chief_allowed?).and_return(true)
      allow_any_instance_of(Profile::LineChief).to receive(:should_schedule_vacation).and_return([])
      allow_any_instance_of(VacationReadyToSubmitPolicy).to receive(:planning_completed?).and_return(false)
      allow_any_instance_of(VacationReadyToSubmitPolicy).to receive(:chief_all_scheduled?).and_return(false)
    end

    subject { get permission_url, params: { scope: 'vacation_ready_to_submit', action: 'show', profile_id: @chief.profile.id } }

    it 'receives LineChief with profile_id call' do
      lc = Profile::LineChief.find(@chief.profile.id)
      expect(Profile::LineChief).to receive(:find).with(@chief.profile.id.to_s).and_return(lc)

      subject
    end

    it 'returns positive response' do
      subject
      expect(response.code).to eq('200')
    end

    context 'when request some people need to submit' do
      it 'returns negative response' do
        allow_any_instance_of(Profile::LineChief).to receive(:should_schedule_vacation).and_return(['Some unsubmitted'])
        subject
        expect(response.code).to eq('403')
      end
    end
  end

  describe 'GET permissions to show banner' do
    let(:permission_url) { '/api/permissions' }
    let(:setting) { Setting.where(name: FEEDBACK_BANNER).first_or_create }

    subject { get permission_url, params: { scope: 'feedback_banner_available', action: 'show' } }

    it 'returns positive response' do
      setting.update!(mode: true, data: 'test')
      subject
      expect(response.code).to eq('200')
    end

    it 'returns 403 unauthorized code' do
      setting.update!(mode: false, data: 'test')
      subject
      expect(response.code).to eq('403')
    end
  end
end

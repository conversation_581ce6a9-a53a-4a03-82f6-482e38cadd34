# frozen_string_literal: true

RSpec.describe GratitudesAPI do
  before(:each) do
    @account = create_and_sign_in_with_account
  end

  describe 'POST give a gratitude to collegue' do
    let(:request_url) { '/api/gratitudes/give' }
    let(:category) { Gratitude.categories.keys.first }

    context 'validation errors' do
      it 'returns 404 when recipient_id is absent' do
        post request_url, params: { recipient_id: -1, category: category }
        expect(response.status).to eq 404
      end

      it 'returns 422 response error when category is not suitable' do
        post request_url, params: { recipient_id: @account.profile.id, category: Faker::Company.name }
        expect(response.status).to eq 422
      end

      context 'when comment length is not enough' do
        let(:comment_error_msg) do
          I18n.t 'activerecord.errors.gratitudes.comment_size_exceed',
                 min_size: MIN_LENGTH_GRATITUDE_COMMENT,
                 max_size: MAX_LENGTH_GRATITUDE_COMMENT
        end

        before do
          post request_url, params: {
            recipient_id: @account.profile.id,
            category: category,
            comment: Faker::Lorem.word.first(3)
          }
        end

        it 'returns 422 response error' do
          expect(response.status).to eq 422
        end

        it 'returns some error msg' do
          expect json_response['base'].include? comment_error_msg
        end
      end
    end
  end
end

# frozen_string_literal: true

RSpec.describe PageCategoriesAPI do
  before(:each) do
    categories = create_list(:category, 8)

    categories.each_with_index do |category_item, index|
      create_pages_for_category(category_item) if [0, 1].include?(index)
    end
  end

  before(:each) do
    create_and_sign_in_with_account
  end

  after(:each) do
    ActiveStorage::Attachment.all.each(&:purge)
  end

  describe 'GET categories list' do
    before(:each) do
      get '/api/page_categories'
    end

    it 'return http success 200' do
      expect(response.status).to eq(200)
    end

    it 'return array in JSON format' do
      expect(JSON.parse(response.body)).to be_instance_of(Array)
    end

    it 'return categories with id, title, description, icon' do
      category = JSON.parse(response.body).sample

      expect(category).to include('title', 'description', 'id', 'icon')
    end

    it 'doesnt return empty categories' do
      expect(JSON.parse(response.body).length).to eq(2)
    end
  end

  describe 'GET category item' do
    context 'when category exists' do
      before(:each) do
        category = Category.all.sample

        create_pages_for_category(category)

        get "/api/page_categories/#{category.id}"
      end

      it 'return http success 200' do
        expect(response.status).to eq(200)
      end

      it 'return hash in JSON format' do
        expect(JSON.parse(response.body)).to be_instance_of(Hash)
      end

      it 'return category with id, title, description, icon' do
        expect(JSON.parse(response.body)).to include('title', 'description', 'id', 'icon')
      end

      it 'return list of pages with id, title and description' do
        page = JSON.parse(response.body)['pages'].sample

        expect(page).to include('id', 'title', 'description')
      end
    end

    context 'when category does not exists' do
      it 'return http not found 404' do
        get '/api/page_categories/jslfsjf'

        expect(response.status).to eq(404)
      end
    end
  end
end

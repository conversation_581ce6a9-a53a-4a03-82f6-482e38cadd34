FROM openjdk:8u212-jre-alpine

RUN apk update

RUN addgroup -S etl && adduser -S etl -G etl
RUN chown -R etl /home/<USER>
USER etl

WORKDIR /home/<USER>

COPY ./pentaho-jobs/ /home/<USER>/pentaho-jobs/

CMD ["sh", "-c", "./pentaho/kitchen.sh -file:./pentaho-jobs/Intranet.kjb -param:INTRANET_DB_HOST=$INTRANET_DB_HOST -param:INTRANET_DB_PORT=$INTRANET_DB_PORT -param:INTRANET_DB_NAME=$INTRANET_DB_NAME -param:INTRANET_DB_USER=$INTRANET_DB_USER -param:INTRANET_DB_PASS=$INTRANET_DB_PASS -param:PDB_HOST=$PDB_HOST -param:PDB_PORT=$PDB_PORT -param:PDB_NAME=$PDB_NAME -param:PDB_USER=$PDB_USER -param:PDB_PASS=$PDB_PASS"]
